const express = require('express');
const router = express.Router();
const { query } = require('../config/db');
const { authenticateJWT, isAdmin } = require('../middleware/auth');

/**
 * @route   GET /api/products
 * @desc    Get all products with optional filtering
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const { category, brand, min_price, max_price, search, sort, limit = 10, page = 1 } = req.query;
    
    let queryText = `
      SELECT p.*, b.name as brand_name, c.name as category_name, 
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image,
             (SELECT ROUND(AVG(rating), 1) FROM reviews WHERE product_id = p.product_id) as average_rating,
             (SELECT COUNT(*) FROM reviews WHERE product_id = p.product_id) as review_count
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      LEFT JOIN categories c ON p.category_id = c.category_id
      WHERE 1=1
    `;
    
    const queryParams = [];
    let paramCount = 1;
    
    // Add filters if provided
    if (category) {
      queryText += ` AND c.category_id = $${paramCount}`;
      queryParams.push(category);
      paramCount++;
    }
    
    if (brand) {
      queryText += ` AND b.brand_id = $${paramCount}`;
      queryParams.push(brand);
      paramCount++;
    }
    
    if (min_price) {
      queryText += ` AND p.price >= $${paramCount}`;
      queryParams.push(min_price);
      paramCount++;
    }
    
    if (max_price) {
      queryText += ` AND p.price <= $${paramCount}`;
      queryParams.push(max_price);
      paramCount++;
    }
    
    if (search) {
      queryText += ` AND (p.name ILIKE $${paramCount} OR p.description ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }
    
    // Add sorting
    if (sort) {
      const sortParams = sort.split(':');
      const sortField = sortParams[0];
      const sortOrder = sortParams[1]?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
      
      const allowedSortFields = ['price', 'name', 'created_at'];
      if (allowedSortFields.includes(sortField)) {
        queryText += ` ORDER BY p.${sortField} ${sortOrder}`;
      } else {
        queryText += ` ORDER BY p.created_at DESC`;
      }
    } else {
      queryText += ` ORDER BY p.created_at DESC`;
    }
    
    // Add pagination
    const offset = (page - 1) * limit;
    queryText += ` LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
    queryParams.push(limit, offset);
    
    const result = await query(queryText, queryParams);
    
    // Get total count for pagination
    const countQueryText = `
      SELECT COUNT(*) as total 
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      LEFT JOIN categories c ON p.category_id = c.category_id
      WHERE 1=1
    `;
    
    // Add the same filters to count query
    let countQueryParams = [];
    let countParamCount = 1;
    
    if (category) {
      countQueryText += ` AND c.category_id = $${countParamCount}`;
      countQueryParams.push(category);
      countParamCount++;
    }
    
    if (brand) {
      countQueryText += ` AND b.brand_id = $${countParamCount}`;
      countQueryParams.push(brand);
      countParamCount++;
    }
    
    if (min_price) {
      countQueryText += ` AND p.price >= $${countParamCount}`;
      countQueryParams.push(min_price);
      countParamCount++;
    }
    
    if (max_price) {
      countQueryText += ` AND p.price <= $${countParamCount}`;
      countQueryParams.push(max_price);
      countParamCount++;
    }
    
    if (search) {
      countQueryText += ` AND (p.name ILIKE $${countParamCount} OR p.description ILIKE $${countParamCount})`;
      countQueryParams.push(`%${search}%`);
      countParamCount++;
    }
    
    const countResult = await query(countQueryText, countQueryParams);
    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / limit);
    
    res.json({
      success: true,
      products: result.rows,
      pagination: {
        total_items: totalItems,
        total_pages: totalPages,
        current_page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching products'
    });
  }
});

/**
 * @route   GET /api/products/featured
 * @desc    Get featured products
 * @access  Public
 */
router.get('/featured', async (req, res) => {
  try {
    const result = await query(`
      SELECT p.*, b.name as brand_name, c.name as category_name,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image,
             (SELECT ROUND(AVG(rating), 1) FROM reviews WHERE product_id = p.product_id) as average_rating
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      LEFT JOIN categories c ON p.category_id = c.category_id
      WHERE p.product_id IN (
        SELECT DISTINCT product_id FROM product_promotions
        WHERE promotion_id IN (
          SELECT promotion_id FROM promotions WHERE is_active = true
        )
      )
      ORDER BY p.created_at DESC
      LIMIT 8
    `);
    
    res.json({
      success: true,
      featured_products: result.rows
    });
  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching featured products'
    });
  }
});

/**
 * @route   GET /api/products/:id
 * @desc    Get product by ID
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get product details
    const productResult = await query(`
      SELECT p.*, b.name as brand_name, c.name as category_name,
             (SELECT ROUND(AVG(rating), 1) FROM reviews WHERE product_id = p.product_id) as average_rating,
             (SELECT COUNT(*) FROM reviews WHERE product_id = p.product_id) as review_count
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      LEFT JOIN categories c ON p.category_id = c.category_id
      WHERE p.product_id = $1
    `, [id]);
    
    if (productResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    const product = productResult.rows[0];
    
    // Get product images
    const imagesResult = await query(
      'SELECT * FROM product_images WHERE product_id = $1 ORDER BY is_primary DESC',
      [id]
    );
    
    // Get product specifications
    const specificationsResult = await query(
      'SELECT * FROM specifications WHERE product_id = $1',
      [id]
    );
    
    // Get product reviews
    const reviewsResult = await query(`
      SELECT r.*, u.username, u.first_name, u.last_name
      FROM reviews r
      JOIN users u ON r.user_id = u.user_id
      WHERE r.product_id = $1
      ORDER BY r.created_at DESC
      LIMIT 5
    `, [id]);
    
    // Get related products (same category)
    const relatedResult = await query(`
      SELECT p.product_id, p.name, p.price,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM products p
      WHERE p.category_id = $1 AND p.product_id != $2
      ORDER BY RANDOM()
      LIMIT 4
    `, [product.category_id, id]);
    
    res.json({
      success: true,
      product: {
        ...product,
        images: imagesResult.rows,
        specifications: specificationsResult.rows,
        reviews: reviewsResult.rows,
        related_products: relatedResult.rows
      }
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching product'
    });
  }
});

/**
 * @route   POST /api/products
 * @desc    Create a new product
 * @access  Private/Admin
 */
router.post('/', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { 
      name, description, price, stock_quantity, brand_id, category_id, 
      release_date, specifications, images 
    } = req.body;
    
    // Insert product
    const productResult = await query(
      `INSERT INTO products 
       (name, description, price, stock_quantity, brand_id, category_id, release_date, specifications) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [name, description, price, stock_quantity, brand_id, category_id, release_date, JSON.stringify(specifications)]
    );
    
    const product = productResult.rows[0];
    
    // Insert product images if provided
    if (images && images.length > 0) {
      for (let i = 0; i < images.length; i++) {
        await query(
          'INSERT INTO product_images (product_id, image_url, is_primary) VALUES ($1, $2, $3)',
          [product.product_id, images[i].image_url, images[i].is_primary || i === 0]
        );
      }
    }
    
    // Insert product specifications if provided
    if (specifications) {
      for (const [name, value] of Object.entries(specifications)) {
        await query(
          'INSERT INTO specifications (product_id, name, value) VALUES ($1, $2, $3)',
          [product.product_id, name, value]
        );
      }
    }
    
    res.status(201).json({
      success: true,
      product
    });
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating product'
    });
  }
});

/**
 * @route   PUT /api/products/:id
 * @desc    Update a product
 * @access  Private/Admin
 */
router.put('/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, description, price, stock_quantity, brand_id, category_id, 
      release_date, specifications, images 
    } = req.body;
    
    // Check if product exists
    const checkResult = await query(
      'SELECT * FROM products WHERE product_id = $1',
      [id]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Update product
    const productResult = await query(
      `UPDATE products 
       SET name = $1, description = $2, price = $3, stock_quantity = $4, 
           brand_id = $5, category_id = $6, release_date = $7, 
           specifications = $8, updated_at = NOW()
       WHERE product_id = $9 
       RETURNING *`,
      [name, description, price, stock_quantity, brand_id, category_id, 
       release_date, JSON.stringify(specifications), id]
    );
    
    // Update product images if provided
    if (images) {
      // Delete existing images
      await query('DELETE FROM product_images WHERE product_id = $1', [id]);
      
      // Insert new images
      for (let i = 0; i < images.length; i++) {
        await query(
          'INSERT INTO product_images (product_id, image_url, is_primary) VALUES ($1, $2, $3)',
          [id, images[i].image_url, images[i].is_primary || i === 0]
        );
      }
    }
    
    // Update specifications if provided
    if (specifications) {
      // Delete existing specifications
      await query('DELETE FROM specifications WHERE product_id = $1', [id]);
      
      // Insert new specifications
      for (const [name, value] of Object.entries(specifications)) {
        await query(
          'INSERT INTO specifications (product_id, name, value) VALUES ($1, $2, $3)',
          [id, name, value]
        );
      }
    }
    
    res.json({
      success: true,
      product: productResult.rows[0]
    });
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating product'
    });
  }
});

/**
 * @route   DELETE /api/products/:id
 * @desc    Delete a product
 * @access  Private/Admin
 */
router.delete('/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if product exists
    const checkResult = await query(
      'SELECT * FROM products WHERE product_id = $1',
      [id]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Delete product (cascade will handle related records)
    await query('DELETE FROM products WHERE product_id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting product'
    });
  }
});

module.exports = router; 