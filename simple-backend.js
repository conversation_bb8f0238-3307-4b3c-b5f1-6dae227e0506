const express = require('./backend/node_modules/express');
const cors = require('./backend/node_modules/cors');
const path = require('path');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors({
  origin: 'http://localhost:8080',
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Health check
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    status: 'OK', 
    message: 'RaoufStore Backend Server is running',
    timestamp: new Date().toISOString()
  });
});

// Simple products endpoint (using sample data for now)
app.get('/api/products', (req, res) => {
  console.log('Products requested');
  const sampleProducts = [
    {
      product_id: 1,
      name: 'iPhone 13 Pro Max',
      price: 147000,
      brand_name: 'Apple',
      category_name: 'Smartphones',
      stock_quantity: 15,
      description: 'Latest iPhone with advanced features',
      primary_image: 'images/real-products/iphone13_main.jpg'
    },
    {
      product_id: 2,
      name: 'Samsung Galaxy S21',
      price: 160000,
      brand_name: 'Samsung',
      category_name: 'Smartphones',
      stock_quantity: 20,
      description: 'Samsung flagship smartphone',
      primary_image: 'images/real-products/samsung_s21_main.jpg'
    }
  ];
  
  res.json({
    success: true,
    products: sampleProducts,
    count: sampleProducts.length
  });
});

// Featured products endpoint
app.get('/api/products/featured', (req, res) => {
  console.log('Featured products requested');
  const featuredProducts = [
    {
      product_id: 1,
      name: 'iPhone 13 Pro Max',
      price: 147000,
      description: 'Apple\'s flagship smartphone with a 6.7-inch display'
    },
    {
      product_id: 2,
      name: 'Samsung Galaxy S21',
      price: 160000,
      description: 'Samsung\'s flagship smartphone with a 6.2-inch display'
    }
  ];
  
  res.json({
    success: true,
    featured_products: featuredProducts
  });
});

// Catch all for API routes
app.use('/api/*', (req, res) => {
  console.log(`API endpoint not found: ${req.method} ${req.url}`);
  res.status(404).json({
    success: false,
    message: `API endpoint not found: ${req.method} ${req.url}`
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RaoufStore Backend Server running on port ${PORT}`);
  console.log(`📡 API accessible at http://localhost:${PORT}/api`);
  console.log(`🌐 Frontend URL: http://localhost:8080`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
