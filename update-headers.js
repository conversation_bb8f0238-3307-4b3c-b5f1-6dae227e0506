const fs = require('fs');
const path = require('path');

// List of HTML files to update
const htmlFiles = [
    'frontend/about.html',
    'frontend/cart.html',
    'frontend/contact.html',
    'frontend/product-details.html'
];

// Old header actions HTML
const oldHeaderActions = `            <div class="header-actions">
                <a href="cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <a href="login.html" class="login-btn">Login</a>
            </div>`;

// New header actions HTML
const newHeaderActions = `            <div class="header-actions">
                <a href="cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="login.html" class="login-btn">Login</a>
                    <a href="login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>`;

console.log('🔄 Updating header actions in HTML files...');

htmlFiles.forEach(filePath => {
    try {
        if (fs.existsSync(filePath)) {
            let content = fs.readFileSync(filePath, 'utf8');
            
            if (content.includes(oldHeaderActions)) {
                content = content.replace(oldHeaderActions, newHeaderActions);
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`✅ Updated: ${filePath}`);
            } else {
                console.log(`⚠️  Pattern not found in: ${filePath}`);
            }
        } else {
            console.log(`❌ File not found: ${filePath}`);
        }
    } catch (error) {
        console.error(`❌ Error updating ${filePath}:`, error.message);
    }
});

console.log('🎉 Header update completed!');
