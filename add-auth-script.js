const fs = require('fs');
const path = require('path');

// List of HTML files to update
const htmlFiles = [
    'frontend/products.html',
    'frontend/about.html',
    'frontend/contact.html',
    'frontend/cart.html',
    'frontend/product-details.html',
    'frontend/login.html'
];

console.log('🔄 Adding auth.js script to HTML files...');

htmlFiles.forEach(filePath => {
    try {
        if (fs.existsSync(filePath)) {
            let content = fs.readFileSync(filePath, 'utf8');
            
            // Check if auth.js is already included
            if (content.includes('js/auth.js')) {
                console.log(`⚠️  auth.js already included in: ${filePath}`);
                return;
            }
            
            // Find the pattern to replace
            const patterns = [
                // Pattern 1: Just before app.js
                {
                    old: '<script src="js/app.js"></script>',
                    new: '<script src="js/auth.js"></script>\n    <script src="js/app.js"></script>'
                },
                // Pattern 2: Just before login.js
                {
                    old: '<script src="js/login.js"></script>',
                    new: '<script src="js/auth.js"></script>\n    <script src="js/login.js"></script>'
                },
                // Pattern 3: Before other js files
                {
                    old: '<script src="js/',
                    new: '<script src="js/auth.js"></script>\n    <script src="js/'
                }
            ];
            
            let updated = false;
            for (const pattern of patterns) {
                if (content.includes(pattern.old)) {
                    content = content.replace(pattern.old, pattern.new);
                    updated = true;
                    break;
                }
            }
            
            if (updated) {
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`✅ Updated: ${filePath}`);
            } else {
                console.log(`⚠️  No suitable pattern found in: ${filePath}`);
            }
        } else {
            console.log(`❌ File not found: ${filePath}`);
        }
    } catch (error) {
        console.error(`❌ Error updating ${filePath}:`, error.message);
    }
});

console.log('🎉 Auth script addition completed!');
