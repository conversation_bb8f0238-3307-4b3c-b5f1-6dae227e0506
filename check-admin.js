const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// Create a connection pool
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'ecommerce_phones'
});

async function checkAdmin() {
  try {
    // Query the database for the admin user
    const result = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (result.rows.length === 0) {
      console.log('Admin user not found in the database');
      
      // Create admin user if it doesn't exist
      console.log('Creating admin user...');
      const salt = await bcrypt.genSalt(10);
      const password_hash = await bcrypt.hash('Admin@123', salt);
      
      await pool.query(
        'INSERT INTO users (username, email, password_hash, first_name, last_name, phone_number, address, role) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
        ['admin', '<EMAIL>', password_hash, 'Admin', 'User', '************', '123 Admin St, Tech City, TC 12345', 'admin']
      );
      
      console.log('Admin user created successfully');
    } else {
      console.log('Admin user found in the database:');
      const admin = result.rows[0];
      console.log(`ID: ${admin.user_id}`);
      console.log(`Username: ${admin.username}`);
      console.log(`Email: ${admin.email}`);
      console.log(`Role: ${admin.role}`);
      
      // Check if the password is correctly hashed
      const testPassword = 'Admin@123';
      const isMatch = await bcrypt.compare(testPassword, admin.password_hash);
      
      if (isMatch) {
        console.log('Password is correctly hashed and matches "Admin@123"');
      } else {
        console.log('Password does not match "Admin@123"');
        
        // Update the password
        console.log('Updating admin password...');
        const salt = await bcrypt.genSalt(10);
        const password_hash = await bcrypt.hash('Admin@123', salt);
        
        await pool.query(
          'UPDATE users SET password_hash = $1 WHERE user_id = $2',
          [password_hash, admin.user_id]
        );
        
        console.log('Admin password updated successfully');
      }
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    pool.end();
  }
}

checkAdmin(); 