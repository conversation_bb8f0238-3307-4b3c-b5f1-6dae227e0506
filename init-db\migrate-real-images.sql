-- Migration file to update product images with real image paths

-- First, clear existing images
DELETE FROM product_images;

-- Insert main product images (primary images)
INSERT INTO product_images (product_id, image_url, is_primary) VALUES
(1, 'images/real-products/iphone13_main.jpg', true),
(2, 'images/real-products/samsung_s21_main.jpg', true),
(3, 'images/real-products/pixel6_main.jpg', true),
(4, 'images/real-products/oneplus9_main.jpg', true),
(5, 'images/real-products/xiaomi12_main.jpg', true),
(6, 'images/real-products/ipad_pro_main.jpg', true),
(7, 'images/real-products/galaxy_tab_main.jpg', true),
(8, 'images/real-products/premium_case_main.jpg', true),
(9, 'images/real-products/fast_charger_main.jpg', true),
(10, 'images/real-products/screen_protector_main.jpg', true);

-- Insert additional product images for smartphones
INSERT INTO product_images (product_id, image_url, is_primary) VALUES
-- iPhone additional images
(1, 'images/real-products/iphone13_1.jpg', false),
(1, 'images/real-products/iphone13_2.jpg', false),
(1, 'images/real-products/iphone13_3.jpg', false),

-- Samsung additional images
(2, 'images/real-products/samsung_s21_1.jpg', false),
(2, 'images/real-products/samsung_s21_2.jpg', false),

-- Google Pixel additional images
(3, 'images/real-products/pixel6_1.jpg', false),
(3, 'images/real-products/pixel6_2.jpg', false),

-- OnePlus additional images
(4, 'images/real-products/oneplus9_1.jpg', false),

-- Xiaomi additional images
(5, 'images/real-products/xiaomi12_1.jpg', false),

-- iPad additional images
(6, 'images/real-products/ipad_pro_1.jpg', false),
(6, 'images/real-products/ipad_pro_2.jpg', false),

-- Samsung Galaxy Tab additional images
(7, 'images/real-products/galaxy_tab_1.jpg', false);

-- Update product descriptions to be more detailed
UPDATE products SET description = 'The iPhone 13 Pro Max features a stunning 6.7-inch Super Retina XDR display, powerful A15 Bionic chip, and a professional camera system with 3x optical zoom. Available in various storage options and colors.' WHERE product_id = 1;

UPDATE products SET description = 'Samsung Galaxy S21 Ultra with a 108MP camera, 8K video recording, and 5G connectivity. Features a 6.8-inch Dynamic AMOLED 2X display and all-day battery life.' WHERE product_id = 2;

UPDATE products SET description = 'Google Pixel 6 Pro with Google Tensor, the first processor designed by Google specifically for Pixel. Features a 50MP wide and 12MP ultrawide camera, and intelligent battery that can last beyond 24 hours.' WHERE product_id = 3;

UPDATE products SET description = 'OnePlus 9 Pro with Hasselblad Camera for Mobile. Features Fluid Display 2.0 with Smart 120 Hz, Snapdragon 888, and Warp Charge 65T.' WHERE product_id = 4;

UPDATE products SET description = 'Xiaomi 12 Pro featuring the latest Snapdragon 8 Gen 1 processor, 120W HyperCharge, and a triple 50MP camera array for professional-grade photography.' WHERE product_id = 5;

UPDATE products SET description = 'iPad Pro with the breakthrough M1 chip, Ultra Wide camera with Center Stage, and 11-inch Liquid Retina display. Perfect for work, creativity, and play.' WHERE product_id = 6;

UPDATE products SET description = 'Samsung Galaxy Tab S8 featuring a large 12.4-inch display, Snapdragon processor, and S Pen included. Perfect for productivity and entertainment on the go.' WHERE product_id = 7;

UPDATE products SET description = 'Premium phone case offers military-grade drop protection while maintaining a slim profile. Compatible with wireless charging and available in multiple colors.' WHERE product_id = 8;

UPDATE products SET description = 'Fast charger with 65W output for rapid charging of your smartphone or tablet. Features advanced safety protection and compact design.' WHERE product_id = 9;

UPDATE products SET description = 'Tempered glass screen protector with 9H hardness, oleophobic coating, and easy installation. Provides edge-to-edge protection for your device.' WHERE product_id = 10; 