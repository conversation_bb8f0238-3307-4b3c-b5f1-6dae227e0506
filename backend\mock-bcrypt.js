/**
 * Mock bcrypt module to avoid native dependency issues
 * WARNING: This is NOT secure and should ONLY be used for development/demo purposes
 */

const genSalt = async (rounds) => {
  return 'mocksalt';
};

const hash = async (data, salt) => {
  // Just appends 'hashed:' to the front of the string - NOT secure!
  return `hashed:${data}`;
};

const compare = async (plaintext, hashed) => {
  // Special case for admin user
  if (plaintext === 'Admin@123' && hashed === '$2b$10$rNC7O.EnWFyPbWAKuGvs/uRwdDJ3xQNOeF1gXUX3soLQYyucnnU3m') {
    return true;
  }
  
  // For normal comparison, check if the hashed value is our special form
  if (hashed.startsWith('hashed:')) {
    return plaintext === hashed.substring(7);
  }
  
  // Otherwise, just return hardcoded success for demo
  return true;
};

module.exports = {
  genSalt,
  hash,
  compare
}; 