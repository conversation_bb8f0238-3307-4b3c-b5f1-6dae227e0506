require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const { authenticateJWT } = require('./middleware/auth');
const { pool } = require('./config/db');

// Import routes
const usersRouter = require('./routes/users');
const productsRouter = require('./routes/products');
const cartsRouter = require('./routes/carts');
const ordersRouter = require('./routes/orders');
const adminRouter = require('./routes/admin');

// Initialize express app
const app = express();

// Set port
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:8080',
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true }));

// Static files
app.use('/images', express.static(path.join(__dirname, '../frontend/images')));

// Log requests for development
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  next();
});

// API Routes
app.use('/api/users', usersRouter);
app.use('/api/products', productsRouter);
app.use('/api/carts', authenticateJWT, cartsRouter);
app.use('/api/orders', authenticateJWT, ordersRouter);
app.use('/api', adminRouter); // Routes admin accessibles via /api/categories, /api/brands, etc.

// Health check route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Root route
app.get('/api', (req, res) => {
  res.json({
    message: 'Welcome to RaoufStore E-Commerce API',
    endpoints: {
      products: '/api/products',
      users: '/api/users',
      carts: '/api/carts',
      orders: '/api/orders',
      admin: '/api'
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Something went wrong on the server',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RaoufStore Backend Server running on port ${PORT}`);
  console.log(`📡 API accessible at http://localhost:${PORT}/api`);
  console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL}`);
  console.log(`🗄️  Database: ${process.env.DB_NAME} on ${process.env.DB_HOST}:${process.env.DB_PORT}`);
});

// Handle shutdown gracefully
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  
  // Close database connection
  pool.end(() => {
    console.log('Database connection closed');
    process.exit(0);
  });
});

module.exports = app; 