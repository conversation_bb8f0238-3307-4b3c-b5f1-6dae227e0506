// ===== DATA MANAGER - GLOBAL DATA SYNCHRONIZATION =====
// Global data storage
window.RaoufStoreData = {
    users: [],
    orders: [],
    products: [],
    categories: [],
    brands: [],
    initialized: false
};

// Initialize data manager
function initializeDataManager() {
    if (window.RaoufStoreData.initialized) {
        return;
    }

    // Load existing data from localStorage
    loadDataFromStorage();

    if (window.RaoufStoreData.users.length === 0) {
        initializeSampleUsers();
    }

    if (window.RaoufStoreData.orders.length === 0) {
        initializeSampleOrders();
    }

    if (window.RaoufStoreData.products.length === 0) {
        initializeSampleProducts();
    }

    if (window.RaoufStoreData.categories.length === 0) {
        initializeSampleCategories();
    }

    if (window.RaoufStoreData.brands.length === 0) {
        initializeSampleBrands();
    }

    window.RaoufStoreData.initialized = true;
    }

// Load data from localStorage
function loadDataFromStorage() {
    try {
        const storedUsers = localStorage.getItem('raoufstore_users');
        const storedOrders = localStorage.getItem('raoufstore_orders');
        const storedProducts = localStorage.getItem('raoufstore_products');
        const storedCategories = localStorage.getItem('raoufstore_categories');
        const storedBrands = localStorage.getItem('raoufstore_brands');

        if (storedUsers) {
            window.RaoufStoreData.users = JSON.parse(storedUsers);
            }

        if (storedOrders) {
            window.RaoufStoreData.orders = JSON.parse(storedOrders);
            }

        if (storedProducts) {
            window.RaoufStoreData.products = JSON.parse(storedProducts);
            }

        if (storedCategories) {
            window.RaoufStoreData.categories = JSON.parse(storedCategories);
            }

        if (storedBrands) {
            window.RaoufStoreData.brands = JSON.parse(storedBrands);
            }
    } catch (error) {
        console.error('❌ Error loading data from storage:', error);
    }
}

// Save data to localStorage
function saveDataToStorage() {
    try {
        localStorage.setItem('raoufstore_users', JSON.stringify(window.RaoufStoreData.users));
        localStorage.setItem('raoufstore_orders', JSON.stringify(window.RaoufStoreData.orders));
        localStorage.setItem('raoufstore_products', JSON.stringify(window.RaoufStoreData.products));
        localStorage.setItem('raoufstore_categories', JSON.stringify(window.RaoufStoreData.categories));
        localStorage.setItem('raoufstore_brands', JSON.stringify(window.RaoufStoreData.brands));
        } catch (error) {
        console.error('❌ Error saving data to storage:', error);
    }
}

function initializeSampleUsers() {
    window.RaoufStoreData.users = [
        {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'User',
            phone: '************',
            address: '123 Admin St, Admin City, AC 12345',
            role: 'admin',
            joined_date: '2023-01-01',
            orders_count: 0,
            reviews_count: 0,
            total_spent: 0,
            recent_orders: []
        },
        {
            id: 2,
            username: 'johndoe',
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            phone: '************',
            address: '456 Main St, Anytown, AT 67890',
            role: 'customer',
            joined_date: '2023-02-15',
            orders_count: 2,
            reviews_count: 1,
            total_spent: 949.98,
            recent_orders: []
        },
        {
            id: 3,
            username: 'janedoe',
            email: '<EMAIL>',
            first_name: 'Jane',
            last_name: 'Doe',
            phone: '************',
            address: '789 Oak St, Springfield, SP 54321',
            role: 'customer',
            joined_date: '2023-03-01',
            orders_count: 1,
            reviews_count: 0,
            total_spent: 899.99,
            recent_orders: []
        }
    ];
    }

function initializeSampleOrders() {
    window.RaoufStoreData.orders = [
        {
            id: 1001,
            user_id: 2,
            customer_name: 'John Doe',
            customer_email: '<EMAIL>',
            date: new Date().toISOString().split('T')[0],
            total: 599.99,
            status: 'processing',
            payment_status: 'paid',
            shipping_address: '456 Main St, Anytown, AT 67890',
            items: [
                { product_name: 'iPhone 13', quantity: 1, price: 599.99, subtotal: 599.99 }
            ]
        },
        {
            id: 1002,
            user_id: 2,
            customer_name: 'John Doe',
            customer_email: '<EMAIL>',
            date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
            total: 349.99,
            status: 'shipped',
            payment_status: 'paid',
            shipping_address: '456 Main St, Anytown, AT 67890',
            items: [
                { product_name: 'Samsung Galaxy A54', quantity: 1, price: 349.99, subtotal: 349.99 }
            ]
        },
        {
            id: 1003,
            user_id: 3,
            customer_name: 'Jane Doe',
            customer_email: '<EMAIL>',
            date: new Date(Date.now() - 172800000).toISOString().split('T')[0], // 2 days ago
            total: 899.99,
            status: 'delivered',
            payment_status: 'paid',
            shipping_address: '789 Oak St, Springfield, SP 54321',
            items: [
                { product_name: 'iPhone 14 Pro', quantity: 1, price: 899.99, subtotal: 899.99 }
            ]
        }
    ];
    }

// Add new user
function addUser(userData) {
    const newUser = {
        id: getNextUserId(),
        username: userData.username || generateUsername(userData.first_name, userData.last_name),
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone || '',
        address: userData.address || '',
        role: userData.role || 'customer',
        joined_date: new Date().toISOString().split('T')[0],
        orders_count: 0,
        reviews_count: 0,
        total_spent: 0,
        recent_orders: []
    };

    window.RaoufStoreData.users.push(newUser);
    saveDataToStorage();

    return newUser;
}

// Add new order
function addOrder(orderData) {
    const newOrder = {
        id: getNextOrderId(),
        user_id: orderData.user_id,
        customer_name: orderData.customer_name,
        customer_email: orderData.customer_email,
        date: new Date().toISOString().split('T')[0],
        total: orderData.total,
        status: orderData.status || 'pending',
        payment_status: orderData.payment_status || 'pending',
        shipping_address: orderData.shipping_address,
        items: orderData.items || []
    };

    // Decrement stock for each item in the order
    if (newOrder.items && newOrder.items.length > 0) {
        newOrder.items.forEach(item => {
            const success = updateProductStockByName(item.product_name, item.quantity);
            if (success) {
                console.log(`✅ Stock updated for: ${item.product_name}`);
            } else {
                console.warn(`⚠️ Failed to update stock for: ${item.product_name}`);
            }
        });
    }

    window.RaoufStoreData.orders.push(newOrder);

    // Update user statistics
    updateUserStats(orderData.user_id, newOrder);

    saveDataToStorage();

    return newOrder;
}

// Update user statistics
function updateUserStats(userId, order) {
    const user = window.RaoufStoreData.users.find(u => u.id === userId);
    if (user) {
        user.orders_count++;
        user.total_spent += order.total;

        // Add to recent orders (keep only last 5)
        if (!user.recent_orders) user.recent_orders = [];
        user.recent_orders.unshift({
            id: order.id,
            date: order.date,
            total: order.total,
            status: order.status
        });
        user.recent_orders = user.recent_orders.slice(0, 5);

        }
}

// Get next user ID
function getNextUserId() {
    const maxId = window.RaoufStoreData.users.reduce((max, user) => Math.max(max, user.id), 0);
    return maxId + 1;
}

// Get next order ID
function getNextOrderId() {
    const maxId = window.RaoufStoreData.orders.reduce((max, order) => Math.max(max, order.id), 1000);
    return maxId + 1;
}

// Generate username from name
function generateUsername(firstName, lastName) {
    const base = (firstName + lastName).toLowerCase().replace(/[^a-z]/g, '');
    const random = Math.floor(Math.random() * 1000);
    return base + random;
}

// Get user by email
function getUserByEmail(email) {
    return window.RaoufStoreData.users.find(user => user.email === email);
}

// Get user by ID
function getUserById(id) {
    return window.RaoufStoreData.users.find(user => user.id === id);
}

// Get orders by user ID
function getOrdersByUserId(userId) {
    return window.RaoufStoreData.orders.filter(order => order.user_id === userId);
}

// Get all users
function getAllUsers() {
    return window.RaoufStoreData.users;
}

// Get all orders
function getAllOrders() {
    return window.RaoufStoreData.orders;
}

function initializeSampleProducts() {
    window.RaoufStoreData.products = [
        {
            id: 1,
            name: 'iPhone 13',
            price: 599.99,
            stock: 50,
            category: 'iPhone',
            brand: 'Apple',
            image: 'images/iphone-13.jpg'
        },
        {
            id: 2,
            name: 'iPhone 14 Pro',
            price: 120000,
            stock: 30,
            category: 'iPhone',
            brand: 'Apple',
            image: 'images/iphone-14-pro.jpg'
        },
        {
            id: 3,
            name: 'Samsung Galaxy A54',
            price: 349.99,
            stock: 75,
            category: 'Samsung',
            brand: 'Samsung',
            image: 'images/galaxy-a54.jpg'
        },
        {
            id: 4,
            name: 'Samsung Galaxy S23',
            price: 107000,
            stock: 40,
            category: 'Samsung',
            brand: 'Samsung',
            image: 'images/galaxy-s23.jpg'
        },
        {
            id: 5,
            name: 'Google Pixel 7',
            price: 599.99,
            stock: 25,
            category: 'Google',
            brand: 'Google',
            image: 'images/pixel-7.jpg'
        },
        {
            id: 6,
            name: 'OnePlus 11',
            price: 93500,
            stock: 20,
            category: 'OnePlus',
            brand: 'OnePlus',
            image: 'images/oneplus-11.jpg'
        },
        {
            id: 7,
            name: 'Xiaomi 13',
            price: 549.99,
            stock: 35,
            category: 'Xiaomi',
            brand: 'Xiaomi',
            image: 'images/xiaomi-13.jpg'
        },
        {
            id: 8,
            name: 'iPhone 12',
            price: 499.99,
            stock: 60,
            category: 'iPhone',
            brand: 'Apple',
            image: 'images/iphone-12.jpg'
        }
    ];

// Get product by ID
function getProductById(id) {
    return window.RaoufStoreData.products.find(product => product.id === id);
}

// Get product by name
function getProductByName(name) {
    return window.RaoufStoreData.products.find(product => product.name === name);
}

// Update product stock
function updateProductStock(productId, quantity) {
    const product = window.RaoufStoreData.products.find(p => p.id === productId);
    if (product) {
        const oldStock = product.stock;
        product.stock = Math.max(0, product.stock - quantity);
        console.log(`📦 Stock updated for product ${productId}: ${oldStock} → ${product.stock}`);
        saveDataToStorage();
        return true;
    }
    return false;
}

// Update product stock by name
function updateProductStockByName(productName, quantity) {
    const product = window.RaoufStoreData.products.find(p => p.name === productName);
    if (product) {
        const oldStock = product.stock;
        product.stock = Math.max(0, product.stock - quantity);
        console.log(`📦 Stock updated for ${productName}: ${oldStock} → ${product.stock}`);
        saveDataToStorage();
        return true;
    }
    console.warn(`⚠️ Product not found: ${productName}`);
    return false;
}

// Get all products
function getAllProducts() {
    return window.RaoufStoreData.products;
}

// Check if product is in stock
function isProductInStock(productId, quantity = 1) {
    const product = getProductById(productId);
    return product && product.stock >= quantity;
}

// Check if product is in stock by name
function isProductInStockByName(productName, quantity = 1) {
    const product = getProductByName(productName);
    return product && product.stock >= quantity;
}

function initializeSampleCategories() {
    window.RaoufStoreData.categories = [
        { id: 1, name: 'iPhone', description: 'Apple iPhones' },
        { id: 2, name: 'Samsung', description: 'Samsung smartphones' },
        { id: 3, name: 'Google', description: 'Google Pixel phones' },
        { id: 4, name: 'OnePlus', description: 'OnePlus devices' },
        { id: 5, name: 'Xiaomi', description: 'Xiaomi smartphones' },
        { id: 6, name: 'Tablets', description: 'Tablet devices' },
        { id: 7, name: 'Accessories', description: 'Phone accessories' },
        { id: 8, name: 'Wearables', description: 'Smart watches and wearables' }
    ];

function initializeSampleBrands() {
    window.RaoufStoreData.brands = [
        { id: 1, name: 'Apple', logo_url: '', description: 'Apple Inc.' },
        { id: 2, name: 'Samsung', logo_url: '', description: 'Samsung Electronics' },
        { id: 3, name: 'Google', logo_url: '', description: 'Google LLC' },
        { id: 4, name: 'OnePlus', logo_url: '', description: 'OnePlus Technology' },
        { id: 5, name: 'Xiaomi', logo_url: '', description: 'Xiaomi Corporation' },
        { id: 6, name: 'Huawei', logo_url: '', description: 'Huawei Technologies' },
        { id: 7, name: 'Sony', logo_url: '', description: 'Sony Corporation' },
        { id: 8, name: 'LG', logo_url: '', description: 'LG Electronics' }
    ];

// Get all categories
function getAllCategories() {
    return window.RaoufStoreData.categories;
}

// Get all brands
function getAllBrands() {
    return window.RaoufStoreData.brands;
}

// Initialize when script loads with dependency management
function safeInitializeDataManager() {
    try {
        initializeDataManager();

        // Mark dependencies as ready
        if (window.DependencyManager) {
            window.DependencyManager.markReady('RaoufStoreData');
            window.DependencyManager.markReady('DataManager');
        }
    } catch (error) {
        console.error('❌ Error initializing data manager:', error);
        // Retry after a delay
        setTimeout(safeInitializeDataManager, 1000);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', safeInitializeDataManager);
} else {
    // DOM already loaded, initialize immediately
    setTimeout(safeInitializeDataManager, 100);
}

