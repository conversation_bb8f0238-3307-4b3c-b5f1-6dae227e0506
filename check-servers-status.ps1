# Script pour vérifier le statut des serveurs RaoufStore
Write-Host "🔍 Vérification du statut des serveurs RaoufStore..." -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# Fonction pour tester une URL
function Test-Url {
    param($url, $name)
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $name : ACTIF" -ForegroundColor Green
            Write-Host "   URL: $url" -ForegroundColor White
            return $true
        }
    }
    catch {
        Write-Host "❌ $name : INACTIF" -ForegroundColor Red
        Write-Host "   URL: $url" -ForegroundColor White
        Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Fonction pour vérifier les ports
function Test-Port {
    param($port, $name)
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ Port $port ($name) : OUVERT" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Port $port ($name) : FERMÉ" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Port $port ($name) : ERREUR" -ForegroundColor Red
        return $false
    }
}

Write-Host "🔌 VÉRIFICATION DES PORTS :" -ForegroundColor Yellow
$frontendPortOpen = Test-Port -port 8080 -name "Frontend"
$backendPortOpen = Test-Port -port 3001 -name "Backend API"

Write-Host ""
Write-Host "🌐 VÉRIFICATION DES SERVICES :" -ForegroundColor Yellow

# Test Frontend
$frontendActive = Test-Url -url "http://localhost:8080/frontend/index.html" -name "Frontend (Site Web)"

# Test Backend API
$backendActive = Test-Url -url "http://localhost:3001/api/health" -name "Backend API"

# Test pages spécifiques
Write-Host ""
Write-Host "📱 VÉRIFICATION DES PAGES :" -ForegroundColor Yellow
Test-Url -url "http://localhost:8080/frontend/products.html" -name "Page Produits"
Test-Url -url "http://localhost:8080/frontend/admin/index.html" -name "Admin Panel"

Write-Host ""
Write-Host "📊 RÉSUMÉ :" -ForegroundColor Cyan

if ($frontendActive -and $backendActive) {
    Write-Host "🎉 TOUS LES SERVEURS SONT ACTIFS !" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 ACCÈS RAPIDE :" -ForegroundColor White
    Write-Host "   📱 Site Web: http://localhost:8080/frontend/index.html" -ForegroundColor Cyan
    Write-Host "   🛒 Produits: http://localhost:8080/frontend/products.html" -ForegroundColor Cyan
    Write-Host "   🔧 Admin: http://localhost:8080/frontend/admin/index.html" -ForegroundColor Cyan
    Write-Host "   🔗 API: http://localhost:3001/api" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "👤 COMPTES DE TEST :" -ForegroundColor White
    Write-Host "   🔑 Admin: <EMAIL> / Admin@123" -ForegroundColor Yellow
    Write-Host "   👤 Client: <EMAIL> / User@123" -ForegroundColor Yellow
} elseif ($frontendActive) {
    Write-Host "⚠️  FRONTEND ACTIF, BACKEND INACTIF" -ForegroundColor Yellow
    Write-Host "   Le site fonctionne mais l'API n'est pas disponible" -ForegroundColor White
    Write-Host "   📱 Site: http://localhost:8080/frontend/index.html" -ForegroundColor Cyan
} elseif ($backendActive) {
    Write-Host "⚠️  BACKEND ACTIF, FRONTEND INACTIF" -ForegroundColor Yellow
    Write-Host "   L'API fonctionne mais le site n'est pas accessible" -ForegroundColor White
    Write-Host "   🔗 API: http://localhost:3001/api" -ForegroundColor Cyan
} else {
    Write-Host "❌ AUCUN SERVEUR ACTIF" -ForegroundColor Red
    Write-Host "   Lancez les serveurs avec: node start-servers-simple.js" -ForegroundColor White
}

Write-Host ""
Write-Host "🔧 COMMANDES UTILES :" -ForegroundColor Cyan
Write-Host "   Démarrer: node start-servers-simple.js" -ForegroundColor White
Write-Host "   Tester: powershell -File check-servers-status.ps1" -ForegroundColor White
Write-Host "   Arrêter: Ctrl+C dans le terminal des serveurs" -ForegroundColor White

Write-Host ""
Write-Host "✅ Vérification terminée !" -ForegroundColor Green
