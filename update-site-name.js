// Script pour changer le nom du site de PhoneHub à RaoufStore
const fs = require('fs');
const path = require('path');

// Liste des fichiers à modifier
const filesToUpdate = [
    'frontend/about.html',
    'frontend/cart.html',
    'frontend/contact.html',
    'frontend/login.html',
    'frontend/product-details.html',
    'frontend/admin/index.html',
    'frontend/admin/users.html',
    'frontend/admin/orders.html',
    'frontend/admin/products.html',
    'frontend/admin/brands.html',
    'frontend/admin/categories.html',
    'frontend/admin/promotions.html',
    'frontend/admin/login.html',
    'frontend/js/data-manager.js',
    'frontend/js/auth.js',
    'frontend/js/login.js',
    'test-admin-features-complete.html',
    'test-user-order-sync.html',
    'test-brands-final.html',
    'test-brands-flicker-fix.html',
    'test-brands-empty-fix.html',
    'final-admin-test.html'
];

// Fonction pour remplacer le texte dans un fichier
function updateFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  Fichier non trouvé: ${filePath}`);
            return;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Remplacements à effectuer
        const replacements = [
            { from: /PhoneHub/g, to: 'RaoufStore' },
            { from: /phonehub/g, to: 'raoufstore' },
            { from: /info@phonehub\.com/g, to: '<EMAIL>' },
            { from: /PhoneHub - /g, to: 'RaoufStore - ' },
            { from: /- PhoneHub/g, to: '- RaoufStore' }
        ];

        replacements.forEach(replacement => {
            const newContent = content.replace(replacement.from, replacement.to);
            if (newContent !== content) {
                content = newContent;
                modified = true;
            }
        });

        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ Mis à jour: ${filePath}`);
        } else {
            console.log(`ℹ️  Aucun changement: ${filePath}`);
        }
    } catch (error) {
        console.error(`❌ Erreur avec ${filePath}:`, error.message);
    }
}

// Fonction principale
function main() {
    console.log('🔄 Changement du nom du site: PhoneHub → RaoufStore\n');

    filesToUpdate.forEach(file => {
        updateFile(file);
    });

    console.log('\n🎉 Changement de nom terminé!');
    console.log('\n📋 Résumé des changements:');
    console.log('• PhoneHub → RaoufStore');
    console.log('• phonehub → raoufstore');
    console.log('• <EMAIL> → <EMAIL>');
    console.log('\n🔗 Testez votre site: http://localhost:8080');
}

// Exécuter le script
main();
