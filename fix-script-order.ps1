# Script pour corriger l'ordre de chargement des scripts JavaScript
Write-Host "🔧 Correction de l'ordre des scripts JavaScript..." -ForegroundColor Cyan

# Ordre correct des scripts
$correctOrder = @(
    "js/dependency-manager.js",
    "js/data-manager.js", 
    "js/auth.js",
    "js/app.js"
)

# Fichiers HTML à corriger
$htmlFiles = @(
    "frontend\index.html",
    "frontend\products.html",
    "frontend\cart.html",
    "frontend\about.html",
    "frontend\contact.html",
    "frontend\product-details.html"
)

foreach ($file in $htmlFiles) {
    if (Test-Path $file) {
        Write-Host "📝 Traitement de $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        # Vérifier si dependency-manager.js est déjà inclus
        if ($content -notmatch "dependency-manager\.js") {
            # Ajouter dependency-manager.js avant data-manager.js
            $content = $content -replace '(<script src="js/data-manager\.js"></script>)', '<script src="js/dependency-manager.js"></script>`n    $1'
            Write-Host "  ✅ Ajouté dependency-manager.js" -ForegroundColor Green
        }
        
        # Sauvegarder les modifications
        Set-Content $file -Value $content -Encoding UTF8
        Write-Host "  ✅ $file mis à jour" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Fichier non trouvé: $file" -ForegroundColor Yellow
    }
}

# Traitement spécial pour les pages admin
$adminFiles = @(
    "frontend\admin\index.html",
    "frontend\admin\login.html",
    "frontend\admin\users.html",
    "frontend\admin\orders.html",
    "frontend\admin\products.html",
    "frontend\admin\brands.html",
    "frontend\admin\categories.html",
    "frontend\admin\promotions.html"
)

foreach ($file in $adminFiles) {
    if (Test-Path $file) {
        Write-Host "📝 Traitement admin de $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        # Vérifier si dependency-manager.js est déjà inclus
        if ($content -notmatch "dependency-manager\.js") {
            # Ajouter dependency-manager.js au début des scripts
            $content = $content -replace '(<script src="\.\./js/)', '<script src="../js/dependency-manager.js"></script>`n    $1'
            Write-Host "  ✅ Ajouté dependency-manager.js" -ForegroundColor Green
        }
        
        # Sauvegarder les modifications
        Set-Content $file -Value $content -Encoding UTF8
        Write-Host "  ✅ $file mis à jour" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Fichier admin non trouvé: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "✅ Correction de l'ordre des scripts terminée !" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Ordre correct des scripts :" -ForegroundColor Cyan
Write-Host "1. dependency-manager.js (nouveau)" -ForegroundColor White
Write-Host "2. data-manager.js" -ForegroundColor White  
Write-Host "3. auth.js" -ForegroundColor White
Write-Host "4. app.js" -ForegroundColor White
Write-Host "5. [page-specific].js" -ForegroundColor White
Write-Host ""
Write-Host "🧪 Testez maintenant :" -ForegroundColor Cyan
Write-Host "http://localhost:8080/frontend/index.html" -ForegroundColor White
