# PhoneHub E-Commerce Project Launcher
Write-Host "🚀 Starting PhoneHub E-Commerce Platform..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check if PostgreSQL is running
try {
    $pgService = Get-Service postgresql* | Where-Object {$_.Status -eq "Running"}
    if ($pgService) {
        Write-Host "✅ PostgreSQL is running" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL is not running. Please start PostgreSQL service." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ PostgreSQL service not found. Please install PostgreSQL." -ForegroundColor Red
    exit 1
}

# Install backend dependencies if needed
Write-Host "📦 Checking backend dependencies..." -ForegroundColor Yellow
Set-Location backend
if (!(Test-Path "node_modules")) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    npm install
}
Set-Location ..

# Setup database
Write-Host "🗄️ Setting up database..." -ForegroundColor Yellow
node setup-db.js

# Start backend server
Write-Host "🔧 Starting backend server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\backend'; Write-Host '🔧 Backend Server (API)' -ForegroundColor Cyan; Write-Host 'Port: 3000' -ForegroundColor White; Write-Host 'API: http://localhost:3000/api' -ForegroundColor White; Write-Host ''; node server.js"

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend server
Write-Host "🌐 Starting frontend server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host '🌐 Frontend Server' -ForegroundColor Cyan; Write-Host 'Port: 8080' -ForegroundColor White; Write-Host 'Website: http://localhost:8080' -ForegroundColor White; Write-Host 'Admin: http://localhost:8080/admin' -ForegroundColor White; Write-Host ''; node frontend-server.js"

# Wait for servers to start
Start-Sleep -Seconds 5

# Test if servers are running
Write-Host "🧪 Testing servers..." -ForegroundColor Yellow

try {
    $backendTest = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -UseBasicParsing -TimeoutSec 5
    if ($backendTest.StatusCode -eq 200) {
        Write-Host "✅ Backend API is running on http://localhost:3000" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Backend API is not responding" -ForegroundColor Red
}

try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
    if ($frontendTest.StatusCode -eq 200) {
        Write-Host "✅ Frontend is running on http://localhost:8080" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend is not responding" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 PhoneHub E-Commerce Platform is now running!" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Access the application:" -ForegroundColor Cyan
Write-Host "   🌐 Main Website: http://localhost:8080" -ForegroundColor White
Write-Host "   🔧 Admin Panel: http://localhost:8080/admin" -ForegroundColor White
Write-Host "   🔌 API Endpoint: http://localhost:3000/api" -ForegroundColor White
Write-Host ""
Write-Host "👤 Admin Login:" -ForegroundColor Cyan
Write-Host "   📧 Email: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: Admin@123" -ForegroundColor White
Write-Host ""
Write-Host "🛑 To stop the servers, close the PowerShell windows or press Ctrl+C in each terminal" -ForegroundColor Yellow

# Open the website in default browser
Write-Host "🌐 Opening website in browser..." -ForegroundColor Yellow
Start-Process "http://localhost:8080"
