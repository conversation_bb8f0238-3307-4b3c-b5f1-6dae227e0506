<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Register Button - PhoneHub</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .test-link:hover { background: #0056b3; }
        .register-btn-demo { background-color: transparent; color: #007bff; padding: 8px 15px; border: 2px solid #007bff; border-radius: 4px; font-weight: 500; text-decoration: none; display: inline-block; margin: 10px; transition: all 0.3s; }
        .register-btn-demo:hover { background-color: #007bff; color: white; }
        .login-btn-demo { background-color: #007bff; color: white; padding: 8px 15px; border-radius: 4px; font-weight: 500; text-decoration: none; display: inline-block; margin: 10px; transition: background-color 0.3s; }
        .login-btn-demo:hover { background-color: #0056b3; }
        .success { color: #28a745; font-weight: bold; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🧪 Test du Bouton Register - PhoneHub</h1>
    
    <div class="test-section">
        <h2>✅ Problème Résolu</h2>
        <p class="success">Le bouton "Register" est maintenant visible sur toutes les pages du site !</p>
        <p>Avant : Seul le bouton "Login" était visible dans l'en-tête.</p>
        <p>Maintenant : Les deux boutons "Login" et "Register" sont visibles.</p>
    </div>
    
    <div class="test-section">
        <h2>🎨 Aperçu des Boutons</h2>
        <p>Voici à quoi ressemblent les nouveaux boutons :</p>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">
            <a href="#" class="login-btn-demo">Login</a>
            <a href="#" class="register-btn-demo">Register</a>
        </div>
        <p><small>Passez la souris sur les boutons pour voir l'effet hover</small></p>
    </div>
    
    <div class="test-section">
        <h2>🔗 Tests de Navigation</h2>
        <p>Testez les liens suivants pour vérifier le fonctionnement :</p>
        
        <h3>Pages principales avec boutons Login/Register :</h3>
        <a href="http://localhost:8080" class="test-link">🏠 Page d'accueil</a>
        <a href="http://localhost:8080/products.html" class="test-link">📱 Produits</a>
        <a href="http://localhost:8080/about.html" class="test-link">ℹ️ À propos</a>
        <a href="http://localhost:8080/contact.html" class="test-link">📞 Contact</a>
        <a href="http://localhost:8080/cart.html" class="test-link">🛒 Panier</a>
        
        <h3>Tests de connexion/inscription :</h3>
        <a href="http://localhost:8080/login.html" class="test-link">🔑 Login (onglet par défaut)</a>
        <a href="http://localhost:8080/login.html?tab=register" class="test-link">📝 Register (onglet direct)</a>
    </div>
    
    <div class="test-section">
        <h2>📋 Checklist de Vérification</h2>
        <p>Vérifiez les points suivants :</p>
        <ul>
            <li>✅ Le bouton "Register" est visible dans l'en-tête de toutes les pages</li>
            <li>✅ Le bouton "Login" est toujours présent</li>
            <li>✅ Les deux boutons sont alignés horizontalement</li>
            <li>✅ Le bouton "Register" a une bordure bleue et un fond transparent</li>
            <li>✅ Le bouton "Login" a un fond bleu et un texte blanc</li>
            <li>✅ L'effet hover fonctionne sur les deux boutons</li>
            <li>✅ Cliquer sur "Register" ouvre l'onglet d'inscription</li>
            <li>✅ Cliquer sur "Login" ouvre l'onglet de connexion</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🎯 Fonctionnalités Ajoutées</h2>
        <ul>
            <li><strong>Bouton Register visible</strong> : Présent dans l'en-tête de toutes les pages</li>
            <li><strong>Navigation intelligente</strong> : Le lien Register inclut <code>?tab=register</code></li>
            <li><strong>Ouverture automatique</strong> : L'onglet Register s'ouvre automatiquement</li>
            <li><strong>Design cohérent</strong> : Styles harmonieux avec le thème du site</li>
            <li><strong>Responsive</strong> : Fonctionne sur mobile et desktop</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔧 Informations Techniques</h2>
        <p class="info">Modifications apportées :</p>
        <ul>
            <li><strong>HTML</strong> : Ajout du conteneur <code>.auth-buttons</code> dans toutes les pages</li>
            <li><strong>CSS</strong> : Nouveaux styles pour <code>.register-btn</code> et <code>.auth-buttons</code></li>
            <li><strong>JavaScript</strong> : Détection du paramètre URL <code>?tab=register</code></li>
        </ul>
    </div>
    
    <div style="text-align: center; margin: 40px 0;">
        <a href="http://localhost:8080" class="test-link">🏠 Retour au site principal</a>
    </div>
</body>
</html>
