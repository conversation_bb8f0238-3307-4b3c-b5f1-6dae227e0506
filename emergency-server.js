// Serveur d'urgence pour RaoufStore
const express = require('express');
const path = require('path');
const app = express();
const PORT = 8081;

console.log('🚨 Démarrage serveur d\'urgence RaoufStore...');

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'frontend')));
app.use('/frontend', express.static(path.join(__dirname, 'frontend')));

// Routes principales
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

app.get('/index.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

app.get('/products.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'products.html'));
});

app.get('/cart.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'cart.html'));
});

app.get('/login.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'login.html'));
});

// Démarrer le serveur
app.listen(PORT, () => {
    console.log('🚀 SERVEUR D\'URGENCE DÉMARRÉ !');
    console.log(`✅ Site accessible sur: http://localhost:${PORT}`);
    console.log(`🏠 Accueil: http://localhost:${PORT}/frontend/index.html`);
    console.log(`📱 Produits: http://localhost:${PORT}/frontend/products.html`);
    console.log(`🔧 Admin: http://localhost:${PORT}/frontend/admin/login.html`);
    console.log('⏹️  Pour arrêter: Ctrl+C');
}).on('error', (err) => {
    console.error('❌ Erreur serveur d\'urgence:', err);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt serveur d\'urgence...');
    process.exit(0);
});
