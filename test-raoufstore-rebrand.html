<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RaoufStore Rebrand - Site Renommé</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .test-button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.warning { background: #ffc107; color: #212529; }
        .test-button.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .rebrand-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .rebrand-card { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; }
        .rebrand-title { font-weight: bold; margin-bottom: 10px; color: #28a745; }
        .rebrand-desc { color: #666; margin-bottom: 15px; }
        .before-after { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .before, .after { padding: 15px; border-radius: 8px; }
        .before { background: #f8d7da; border-left: 4px solid #dc3545; }
        .after { background: #d4edda; border-left: 4px solid #28a745; }
        .brand-showcase { text-align: center; padding: 30px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border-radius: 12px; margin: 20px 0; }
        .brand-logo { font-size: 48px; margin-bottom: 15px; }
        .brand-name { font-size: 36px; font-weight: bold; margin-bottom: 10px; }
        .brand-tagline { font-size: 18px; opacity: 0.9; }
    </style>
</head>
<body>
    <!-- Header with new brand -->
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>RaoufStore</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="frontend/login.html" class="login-btn">Login</a>
                    <a href="frontend/login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
        <h1>🎨 Test RaoufStore Rebrand</h1>
        
        <div class="brand-showcase">
            <div class="brand-logo">📱</div>
            <div class="brand-name">RaoufStore</div>
            <div class="brand-tagline">Your one-stop shop for the latest smartphones and accessories</div>
        </div>
        
        <div class="test-card">
            <h2>🔄 Changement de Marque Effectué</h2>
            
            <div class="status success">
                <h4>✅ Rebrand Complet : PhoneHub → RaoufStore</h4>
                <ul>
                    <li>✅ <strong>Nom du site</strong> changé dans tous les fichiers HTML</li>
                    <li>✅ <strong>Titres des pages</strong> mis à jour</li>
                    <li>✅ <strong>Logo et navigation</strong> actualisés</li>
                    <li>✅ <strong>Footer et contact</strong> modifiés</li>
                    <li>✅ <strong>Data Manager</strong> renommé (RaoufStoreData)</li>
                    <li>✅ <strong>Email admin</strong> : <EMAIL></li>
                    <li>✅ <strong>LocalStorage</strong> : raoufstore_* keys</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>📋 Changements Effectués</h2>
            
            <div class="rebrand-grid">
                <div class="rebrand-card">
                    <div class="rebrand-title">🏠 Pages Frontend</div>
                    <div class="rebrand-desc">Toutes les pages utilisateur mises à jour</div>
                    <ul>
                        <li>✅ index.html</li>
                        <li>✅ products.html</li>
                        <li>✅ login.html</li>
                        <li>✅ cart.html</li>
                        <li>✅ Headers et footers</li>
                    </ul>
                </div>
                
                <div class="rebrand-card">
                    <div class="rebrand-title">🔧 Pages Admin</div>
                    <div class="rebrand-desc">Interface d'administration rebrandée</div>
                    <ul>
                        <li>✅ admin/index.html</li>
                        <li>✅ admin/users.html</li>
                        <li>✅ admin/orders.html</li>
                        <li>✅ admin/products.html</li>
                        <li>✅ admin/brands.html</li>
                    </ul>
                </div>
                
                <div class="rebrand-card">
                    <div class="rebrand-title">💾 Data Manager</div>
                    <div class="rebrand-desc">Système de données renommé</div>
                    <ul>
                        <li>✅ window.RaoufStoreData</li>
                        <li>✅ raoufstore_users</li>
                        <li>✅ raoufstore_orders</li>
                        <li>✅ raoufstore_products</li>
                        <li>✅ <EMAIL></li>
                    </ul>
                </div>
                
                <div class="rebrand-card">
                    <div class="rebrand-title">📧 Contact Info</div>
                    <div class="rebrand-desc">Informations de contact mises à jour</div>
                    <ul>
                        <li>✅ <EMAIL></li>
                        <li>✅ <EMAIL></li>
                        <li>✅ Copyright 2023 RaoufStore</li>
                        <li>✅ Tous droits réservés</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🔍 Avant / Après</h2>
            
            <div class="before-after">
                <div class="before">
                    <h4>❌ Avant (PhoneHub)</h4>
                    <ul>
                        <li>PhoneHub - Mobile E-commerce</li>
                        <li>window.PhoneHubData</li>
                        <li><EMAIL></li>
                        <li>phonehub_users</li>
                        <li>© 2023 PhoneHub</li>
                    </ul>
                </div>
                
                <div class="after">
                    <h4>✅ Après (RaoufStore)</h4>
                    <ul>
                        <li>RaoufStore - Mobile E-commerce</li>
                        <li>window.RaoufStoreData</li>
                        <li><EMAIL></li>
                        <li>raoufstore_users</li>
                        <li>© 2023 RaoufStore</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🧪 Tests de Vérification</h2>
            
            <div class="status info">
                <h4>Pages à Vérifier :</h4>
                <ol>
                    <li><strong>Page d'accueil</strong> - Logo et nom RaoufStore</li>
                    <li><strong>Page produits</strong> - Navigation cohérente</li>
                    <li><strong>Page login</strong> - Branding mis à jour</li>
                    <li><strong>Admin dashboard</strong> - Interface rebrandée</li>
                    <li><strong>Footer</strong> - Informations de contact</li>
                    <li><strong>Console</strong> - RaoufStoreData initialisé</li>
                </ol>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                <a href="http://localhost:8080/frontend/index.html" class="test-button success">
                    🏠 1. Page d'Accueil
                </a>
                <a href="http://localhost:8080/frontend/products.html" class="test-button success">
                    📱 2. Page Produits
                </a>
                <a href="http://localhost:8080/frontend/login.html" class="test-button success">
                    🔑 3. Page Login
                </a>
                <a href="http://localhost:8080/frontend/cart.html" class="test-button success">
                    🛒 4. Page Panier
                </a>
                <a href="http://localhost:8080/frontend/admin/index.html" class="test-button warning">
                    🔧 5. Admin Dashboard
                </a>
                <a href="http://localhost:8080/frontend/admin/users.html" class="test-button warning">
                    👥 6. Admin Users
                </a>
            </div>
        </div>
        
        <div class="test-card">
            <h2>👤 Comptes de Test</h2>
            
            <div class="status info">
                <h4>Compte Admin (mis à jour) :</h4>
                <ul>
                    <li><strong>Email :</strong> <EMAIL></li>
                    <li><strong>Mot de passe :</strong> Admin@123</li>
                </ul>
                
                <h4>Compte Client :</h4>
                <ul>
                    <li><strong>Email :</strong> <EMAIL></li>
                    <li><strong>Mot de passe :</strong> User@123</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🔍 Console Debug</h2>
            
            <div class="status warning">
                <h4>Messages Console Attendus :</h4>
                <div style="background: #000; color: #00ff00; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    📊 Loading Data Manager...<br>
                    📊 Initializing Data Manager...<br>
                    📊 Loaded users from storage: X<br>
                    📊 Loaded orders from storage: X<br>
                    📊 Loaded products from storage: X<br>
                    ✅ Data Manager initialized<br>
                    👥 Users: X<br>
                    🛒 Orders: X<br>
                    📱 Products: X<br>
                    📊 Data Manager script loaded<br><br>
                    
                    // Vérifier que RaoufStoreData existe<br>
                    console.log(window.RaoufStoreData); // Doit afficher l'objet<br>
                    console.log(window.PhoneHubData); // Doit être undefined
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>📝 Fichiers Modifiés</h2>
            
            <div class="status success">
                <h4>Liste des Fichiers Mis à Jour :</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;">
                    <div style="background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #28a745;">
                        <strong>Frontend</strong><br>
                        <small>index.html<br>products.html<br>login.html<br>cart.html</small>
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Admin</strong><br>
                        <small>admin/index.html<br>admin/users.html<br>admin/orders.html<br>admin/products.html</small>
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #ffc107;">
                        <strong>JavaScript</strong><br>
                        <small>js/data-manager.js<br>Toutes les références<br>PhoneHub → RaoufStore</small>
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 4px; border-left: 3px solid #dc3545;">
                        <strong>Test</strong><br>
                        <small>test-raoufstore-rebrand.html<br>Page de vérification<br>du rebrand</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 12px;">
            <h2>🎨 RaoufStore Rebrand Complete !</h2>
            <p style="font-size: 18px; margin: 20px 0;">
                Nom changé ✅ | Pages mises à jour ✅ | Data Manager renommé ✅ | Contact info ✅
            </p>
            <a href="http://localhost:8080/frontend/index.html" class="test-button success" style="font-size: 16px; padding: 12px 24px;">
                🚀 Découvrir RaoufStore
            </a>
        </div>
    </div>

    <script src="frontend/js/data-manager.js"></script>
    <script src="frontend/js/auth.js"></script>
    <script>
        // Debug logging
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] [RAOUFSTORE REBRAND] ${message}`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('RaoufStore rebrand test page loaded');
            
            // Verify rebrand
            if (window.RaoufStoreData) {
                debugLog('✅ RaoufStoreData found - Rebrand successful!');
                debugLog('Users: ' + window.RaoufStoreData.users.length);
                debugLog('Orders: ' + window.RaoufStoreData.orders.length);
                debugLog('Products: ' + window.RaoufStoreData.products.length);
            } else {
                debugLog('❌ RaoufStoreData not found - Check data-manager.js');
            }
            
            if (window.PhoneHubData) {
                debugLog('⚠️ PhoneHubData still exists - Rebrand incomplete');
            } else {
                debugLog('✅ PhoneHubData removed - Clean rebrand');
            }
        });
    </script>
</body>
</html>
