document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const productsTbody = document.getElementById('products-tbody');
    const productSearch = document.getElementById('product-search');
    const categoryFilter = document.getElementById('category-filter');
    const brandFilter = document.getElementById('brand-filter');
    const statusFilter = document.getElementById('status-filter');
    const addProductBtn = document.getElementById('add-product-btn');
    const productModal = document.getElementById('product-modal');
    const productForm = document.getElementById('product-form');
    const modalTitle = document.getElementById('modal-title');
    const closeBtn = document.querySelector('.close');
    const cancelBtn = document.getElementById('cancel-btn');
    const addImageBtn = document.getElementById('add-image-btn');
    const imageUpload = document.getElementById('image-upload');
    const addSpecBtn = document.getElementById('add-spec-btn');
    const prevPage = document.getElementById('prev-page');
    const nextPage = document.getElementById('next-page');
    const pageNumbers = document.getElementById('page-numbers');

    // State
    let products = [];
    let filteredProducts = [];
    let categories = [];
    let brands = [];
    let currentPage = 1;
    let itemsPerPage = 10;
    let totalPages = 1;
    let editingProductId = null;

    // Initialize
    init();

    // Functions
    async function init() {
        // Load categories
        await loadCategories();
        
        // Load brands
        await loadBrands();
        
        // Load products
        await loadProducts();
        
        // Add event listeners
        addEventListeners();
    }

    function addEventListeners() {
        // Search and filters
        productSearch.addEventListener('input', handleSearch);
        categoryFilter.addEventListener('change', handleFilters);
        brandFilter.addEventListener('change', handleFilters);
        statusFilter.addEventListener('change', handleFilters);
        
        // Product form
        addProductBtn.addEventListener('click', showAddProductModal);
        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        productForm.addEventListener('submit', handleProductSubmit);
        
        // Image upload
        addImageBtn.addEventListener('click', () => imageUpload.click());
        imageUpload.addEventListener('change', handleImageUpload);
        
        // Specifications
        addSpecBtn.addEventListener('click', addSpecificationRow);
        
        // Pagination
        prevPage.addEventListener('click', () => goToPage(currentPage - 1));
        nextPage.addEventListener('click', () => goToPage(currentPage + 1));
        
        // Close modal on outside click
        window.addEventListener('click', (e) => {
            if (e.target === productModal) {
                closeModal();
            }
        });
    }

    async function loadCategories() {
        try {
            // Wait for data manager to be ready
            let attempts = 0;
            while ((!window.RaoufStoreData || !window.RaoufStoreData.categories) && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (window.RaoufStoreData && window.RaoufStoreData.categories) {
                categories = window.RaoufStoreData.categories;
            } else {
                // Fallback categories
                categories = [
                    { id: 1, name: 'iPhone' },
                    { id: 2, name: 'Samsung' },
                    { id: 3, name: 'Google' },
                    { id: 4, name: 'OnePlus' },
                    { id: 5, name: 'Xiaomi' },
                    { id: 6, name: 'Tablets' },
                    { id: 7, name: 'Accessories' },
                    { id: 8, name: 'Wearables' }
                ];
            }

            const categorySelect = document.getElementById('product-category');
            if (categorySelect) {
                categorySelect.innerHTML = '<option value="">Select a category</option>';

                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });

                console.log('✅ Categories loaded:', categories.length);
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            // Don't show alert, just log error
        }
    }
    
    async function loadBrands() {
        try {
            // Wait for data manager to be ready
            let attempts = 0;
            while ((!window.RaoufStoreData || !window.RaoufStoreData.brands) && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (window.RaoufStoreData && window.RaoufStoreData.brands) {
                brands = window.RaoufStoreData.brands;
            } else {
                // Fallback brands
                brands = [
                    { id: 1, name: 'Apple' },
                    { id: 2, name: 'Samsung' },
                    { id: 3, name: 'Google' },
                    { id: 4, name: 'OnePlus' },
                    { id: 5, name: 'Xiaomi' },
                    { id: 6, name: 'Huawei' },
                    { id: 7, name: 'Sony' },
                    { id: 8, name: 'LG' }
                ];
            }

            const brandSelect = document.getElementById('product-brand');
            if (brandSelect) {
                brandSelect.innerHTML = '<option value="">Select a brand</option>';

                brands.forEach(brand => {
                    const option = document.createElement('option');
                    option.value = brand.id;
                    option.textContent = brand.name;
                    brandSelect.appendChild(option);
                });

                console.log('✅ Brands loaded:', brands.length);
            }
        } catch (error) {
            console.error('Error loading brands:', error);
            // Don't show alert, just log error
        }
    }

    async function loadProducts() {
        try {
            console.log('📱 Loading products from API...');

            // Try to load from API first
            const response = await apiRequest('/products?limit=100');

            if (response && response.success && Array.isArray(response.products)) {
                // Convert API format to admin format
                const apiProducts = response.products.map(product => {
                    // Map product names to real product images
                    let imageUrl = '../images/real-products/iphone13_main.jpg'; // Default image

                    if (product.primary_image) {
                        imageUrl = product.primary_image.startsWith('../') ? product.primary_image : `../${product.primary_image}`;
                    } else {
                        // Fallback image mapping
                        if (product.name.toLowerCase().includes('iphone')) {
                            imageUrl = '../images/real-products/iphone13_main.jpg';
                        } else if (product.name.toLowerCase().includes('samsung') || product.name.toLowerCase().includes('galaxy')) {
                            imageUrl = '../images/real-products/samsung_s21_main.jpg';
                        } else if (product.name.toLowerCase().includes('pixel')) {
                            imageUrl = '../images/real-products/pixel6_main.jpg';
                        } else if (product.name.toLowerCase().includes('oneplus')) {
                            imageUrl = '../images/real-products/oneplus9_main.jpg';
                        } else if (product.name.toLowerCase().includes('xiaomi')) {
                            imageUrl = '../images/real-products/xiaomi12_main.jpg';
                        }
                    }

                    return {
                        id: product.product_id,
                        name: product.name,
                        category_id: product.category_id,
                        brand_id: product.brand_id,
                        price: parseFloat(product.price),
                        stock: product.stock_quantity,
                        image: imageUrl,
                        description: product.description || `${product.brand_name || ''} ${product.name}`,
                        specs: [
                            { name: 'Brand', value: product.brand_name || 'Unknown' },
                            { name: 'Category', value: product.category_name || 'Unknown' },
                            { name: 'Price', value: `${parseFloat(product.price).toLocaleString()} DZD` },
                            { name: 'Stock', value: `${product.stock_quantity} units` }
                        ]
                    };
                });

                products = apiProducts;
                console.log('✅ Loaded products from API:', products.length);
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.log('⚠️ API failed, falling back to data manager...');

            // Fallback to global data manager
            if (window.RaoufStoreData && window.RaoufStoreData.products) {
                // Convert data manager format to admin format
                const dataManagerProducts = window.RaoufStoreData.products.map(product => {
                    let imageUrl = '../images/real-products/iphone13_main.jpg';

                    if (product.name.toLowerCase().includes('iphone')) {
                        imageUrl = '../images/real-products/iphone13_main.jpg';
                    } else if (product.name.toLowerCase().includes('samsung')) {
                        imageUrl = '../images/real-products/samsung_s21_main.jpg';
                    }

                    return {
                        id: product.id,
                        name: product.name,
                        category_id: getCategoryIdByName(product.category),
                        brand_id: getBrandIdByName(product.brand),
                        price: product.price,
                        stock: product.stock,
                        image: imageUrl,
                        description: `${product.brand} ${product.name}`,
                        specs: [
                            { name: 'Brand', value: product.brand },
                            { name: 'Category', value: product.category },
                            { name: 'Price', value: `${product.price.toLocaleString()} DZD` },
                            { name: 'Stock', value: `${product.stock} units` }
                        ]
                    };
                });

                products = dataManagerProducts;
                console.log('📱 Loaded products from data manager:', products.length);
            } else {
                // Final fallback to sample data
                products = getSampleProducts();
                console.log('📱 Using sample products:', products.length);
            }
        }

        filteredProducts = [...products];

        // Render products
        renderProducts();

        // Update pagination
        updatePagination();
    }

    function getSampleProducts() {
        return [
            {
                id: 1,
                name: 'iPhone 13',
                category_id: 1,
                brand_id: 1,
                price: 599.99,
                stock: 50,
                image: '../images/real-products/iphone13_main.jpg',
                description: 'Apple iPhone 13',
                specs: [
                    { name: 'Brand', value: 'Apple' },
                    { name: 'Category', value: 'iPhone' },
                    { name: 'Price', value: '80,200 DZD' },
                    { name: 'Stock', value: '50 units' }
                ]
            },
            {
                id: 2,
                name: 'Samsung Galaxy A54',
                category_id: 1,
                brand_id: 2,
                price: 349.99,
                stock: 75,
                image: '../images/real-products/samsung_s21_main.jpg',
                description: 'Samsung Galaxy A54',
                specs: [
                    { name: 'Brand', value: 'Samsung' },
                    { name: 'Category', value: 'Samsung' },
                    { name: 'Price', value: '46,800 DZD' },
                    { name: 'Stock', value: '75 units' }
                ]
            }
        ];
    }

    function getCategoryIdByName(categoryName) {
        // Map category names to IDs
        const categoryMap = {
            'iPhone': 1,
            'Samsung': 1,
            'Google': 1,
            'OnePlus': 1,
            'Xiaomi': 1,
            'Tablets': 2,
            'Accessories': 3,
            'Wearables': 4
        };
        return categoryMap[categoryName] || 1;
    }

    function getBrandIdByName(brandName) {
        // Map brand names to IDs
        const brandMap = {
            'Apple': 1,
            'Samsung': 2,
            'Google': 3,
            'OnePlus': 4,
            'Xiaomi': 5
        };
        return brandMap[brandName] || 1;
    }

    function renderProducts() {
        if (!productsTbody) return;
        
        // Clear table
        productsTbody.innerHTML = '';
        
        // Calculate slice for current page
        const start = (currentPage - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        const currentProducts = filteredProducts.slice(start, end);
        
        // Check if products array is empty
        if (currentProducts.length === 0) {
            productsTbody.innerHTML = `
                <tr>
                    <td colspan="8">No products found.</td>
                </tr>
            `;
            return;
        }
        
        // Create rows for each product
        currentProducts.forEach(product => {
            const tr = document.createElement('tr');
            
            // Get category and brand names
            const category = categories.find(c => c.id == product.category_id) || { name: 'Unknown' };
            const brand = brands.find(b => b.id == product.brand_id) || { name: 'Unknown' };
            
            // Stock status
            let stockStatus = 'in-stock';
            if (product.stock <= 0) {
                stockStatus = 'out-of-stock';
            } else if (product.stock < 10) {
                stockStatus = 'low-stock';
            }
            
            tr.innerHTML = `
                <td>${product.id}</td>
                <td><img src="${product.image}" alt="${product.name}" class="product-thumbnail"></td>
                <td>${product.name}</td>
                <td>${category.name}</td>
                <td>${brand.name}</td>
                <td>${product.price.toLocaleString()} DZD</td>
                <td>
                    <span class="status ${stockStatus}">
                        ${product.stock} ${product.stock <= 0 ? '(Out of Stock)' : product.stock < 10 ? '(Low)' : ''}
                    </span>
                </td>
                <td>
                    <button class="btn small-btn edit-btn" data-id="${product.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn small-btn delete-btn" data-id="${product.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            // Add event listeners for edit and delete buttons
            const editBtn = tr.querySelector('.edit-btn');
            const deleteBtn = tr.querySelector('.delete-btn');
            
            editBtn.addEventListener('click', () => editProduct(product.id));
            deleteBtn.addEventListener('click', () => deleteProduct(product.id));
            
            productsTbody.appendChild(tr);
        });
    }

    function updatePagination() {
        totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
        
        // Update buttons state
        prevPage.disabled = currentPage <= 1;
        nextPage.disabled = currentPage >= totalPages;
        
        // Update page numbers
        renderPageNumbers();
    }
    
    function renderPageNumbers() {
        pageNumbers.innerHTML = '';
        
        // For small number of pages, show all
        if (totalPages <= 5) {
            for (let i = 1; i <= totalPages; i++) {
                addPageNumberButton(i);
            }
            return;
        }
        
        // For many pages, show current page and neighbors
        // Always show first page
        addPageNumberButton(1);
        
        // Show dots if current page is far from first page
        if (currentPage > 3) {
            const dots = document.createElement('span');
            dots.className = 'pagination-dots';
            dots.textContent = '...';
            pageNumbers.appendChild(dots);
        }
        
        // Show pages around current page
        for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
            addPageNumberButton(i);
        }
        
        // Show dots if current page is far from last page
        if (currentPage < totalPages - 2) {
            const dots = document.createElement('span');
            dots.className = 'pagination-dots';
            dots.textContent = '...';
            pageNumbers.appendChild(dots);
        }
        
        // Always show last page
        if (totalPages > 1) {
            addPageNumberButton(totalPages);
        }
    }
    
    function addPageNumberButton(pageNum) {
        const btn = document.createElement('button');
        btn.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
        btn.textContent = pageNum;
        btn.addEventListener('click', () => goToPage(pageNum));
        pageNumbers.appendChild(btn);
    }
    
    function goToPage(page) {
        if (page < 1 || page > totalPages) return;
        
        currentPage = page;
        renderProducts();
        updatePagination();
    }

    function handleSearch() {
        const searchTerm = productSearch.value.toLowerCase();
        
        if (searchTerm.trim() === '') {
            // Reset to all products, but keep other filters
            applyFilters();
        } else {
            // Filter by search term
            filteredProducts = products.filter(product => 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );
            
            // Apply other filters
            applyFilters(false);
        }
        
        // Reset to first page
        currentPage = 1;
        renderProducts();
        updatePagination();
    }
    
    function handleFilters() {
        applyFilters();
        
        // Reset to first page
        currentPage = 1;
        renderProducts();
        updatePagination();
    }
    
    function applyFilters(resetSearch = true) {
        const categoryId = categoryFilter.value;
        const brandId = brandFilter.value;
        const status = statusFilter.value;
        const searchTerm = resetSearch ? '' : productSearch.value.toLowerCase();
        
        // Start with all products
        let result = [...products];
        
        // Apply search filter if not reset
        if (!resetSearch && searchTerm) {
            result = result.filter(product => 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );
        }
        
        // Apply category filter
        if (categoryId) {
            result = result.filter(product => product.category_id == categoryId);
        }
        
        // Apply brand filter
        if (brandId) {
            result = result.filter(product => product.brand_id == brandId);
        }
        
        // Apply status filter
        if (status) {
            result = result.filter(product => {
                if (status === 'in-stock') {
                    return product.stock > 10;
                } else if (status === 'low-stock') {
                    return product.stock > 0 && product.stock <= 10;
                } else if (status === 'out-of-stock') {
                    return product.stock <= 0;
                }
                return true;
            });
        }
        
        filteredProducts = result;
    }

    function showAddProductModal() {
        // Reset form
        productForm.reset();
        document.getElementById('product-id').value = '';
        document.getElementById('image-previews').innerHTML = '';
        
        // Clear specifications except for the first one
        const specsContainer = document.getElementById('specifications-container');
        specsContainer.innerHTML = `
            <div class="spec-row">
                <input type="text" placeholder="Name (e.g. Display)">
                <input type="text" placeholder="Value (e.g. 6.7-inch OLED)">
                <button type="button" class="remove-spec-btn"><i class="fas fa-times"></i></button>
            </div>
        `;
        
        // Add event listener to remove button
        specsContainer.querySelector('.remove-spec-btn').addEventListener('click', function() {
            if (specsContainer.children.length > 1) {
                this.parentElement.remove();
            }
        });
        
        // Update modal title
        modalTitle.textContent = 'Add New Product';
        
        // Reset editing ID
        editingProductId = null;
        
        // Show modal
        productModal.style.display = 'block';
    }
    
    function editProduct(productId) {
        const product = products.find(p => p.id == productId);
        if (!product) return;
        
        // Set editing ID
        editingProductId = productId;
        
        // Update modal title
        modalTitle.textContent = 'Edit Product';
        
        // Fill form with product data
        document.getElementById('product-id').value = product.id;
        document.getElementById('product-name').value = product.name;
        document.getElementById('product-category').value = product.category_id;
        document.getElementById('product-brand').value = product.brand_id;
        document.getElementById('product-price').value = product.price;
        document.getElementById('product-stock').value = product.stock;
        document.getElementById('product-description').value = product.description;
        
        // Add image preview
        const imagePreviewsContainer = document.getElementById('image-previews');
        imagePreviewsContainer.innerHTML = `
            <div class="image-preview">
                <img src="${product.image}" alt="${product.name}">
                <button type="button" class="remove-image-btn"><i class="fas fa-times"></i></button>
            </div>
        `;
        
        // Add event listener to remove image button
        imagePreviewsContainer.querySelector('.remove-image-btn').addEventListener('click', function() {
            this.parentElement.remove();
        });
        
        // Fill specifications
        const specsContainer = document.getElementById('specifications-container');
        specsContainer.innerHTML = '';
        
        if (product.specs && product.specs.length > 0) {
            product.specs.forEach(spec => {
                addSpecificationRow(spec.name, spec.value);
            });
        } else {
            // Add an empty spec row if no specs
            addSpecificationRow();
        }
        
        // Show modal
        productModal.style.display = 'block';
    }
    
    function closeModal() {
        productModal.style.display = 'none';
    }
    
    function handleProductSubmit(e) {
        e.preventDefault();
        
        // Get form data
        const productId = document.getElementById('product-id').value;
        const name = document.getElementById('product-name').value;
        const categoryId = document.getElementById('product-category').value;
        const brandId = document.getElementById('product-brand').value;
        const price = parseFloat(document.getElementById('product-price').value);
        const stock = parseInt(document.getElementById('product-stock').value);
        const description = document.getElementById('product-description').value;
        
        // Validation basique
        if (!name) {
            alert('Please enter a product name');
            return;
        }
        
        if (!price || isNaN(price) || price <= 0) {
            alert('Please enter a valid price');
            return;
        }
        
        if (!stock || isNaN(stock) || stock < 0) {
            alert('Please enter a valid stock quantity');
            return;
        }
        
        // Récupérer l'image téléchargée ou utiliser une image par défaut
        let image = '../images/real-products/iphone13_main.jpg'; // Image par défaut
        
        const imagePreview = document.querySelector('#image-previews img');
        if (imagePreview && imagePreview.src.startsWith('data:image')) {
            // Si une nouvelle image a été téléchargée
            const fileName = imagePreview.dataset.fileName;
            // L'image sera stockée dans le dossier custom-products
            image = `../images/custom-products/${fileName}`;
        } else {
            // Utiliser les images par défaut en fonction du nom du produit
            if (name.toLowerCase().includes('iphone')) {
                image = '../images/real-products/iphone13_main.jpg';
            } else if (name.toLowerCase().includes('samsung') || name.toLowerCase().includes('galaxy')) {
                image = '../images/real-products/samsung_s21_main.jpg';
            } else if (name.toLowerCase().includes('pixel')) {
                image = '../images/real-products/pixel6_main.jpg';
            } else if (name.toLowerCase().includes('oneplus')) {
                image = '../images/real-products/oneplus9_main.jpg';
            } else if (name.toLowerCase().includes('xiaomi')) {
                image = '../images/real-products/xiaomi12_main.jpg';
            } else if (name.toLowerCase().includes('ipad')) {
                image = '../images/real-products/ipad_pro_main.jpg';
            } else if (name.toLowerCase().includes('tab')) {
                image = '../images/real-products/galaxy_tab_main.jpg';
            } else if (name.toLowerCase().includes('case')) {
                image = '../images/real-products/premium_case_main.jpg';
            } else if (name.toLowerCase().includes('charger')) {
                image = '../images/real-products/fast_charger_main.jpg';
            } else if (name.toLowerCase().includes('screen')) {
                image = '../images/real-products/screen_protector_main.jpg';
            }
        }
        
        // Get specifications
        const specs = [];
        const specRows = document.querySelectorAll('#specifications-container .spec-row');
        specRows.forEach(row => {
            const nameInput = row.querySelector('input:first-of-type');
            const valueInput = row.querySelector('input:last-of-type');
            
            if (nameInput.value.trim() && valueInput.value.trim()) {
                specs.push({
                    name: nameInput.value.trim(),
                    value: valueInput.value.trim()
                });
            }
        });
        
        // Create product object
        const product = {
            id: productId || Date.now(),
            name,
            category_id: categoryId || null, // Permettre null pour la catégorie
            brand_id: brandId || null, // Permettre null pour la marque
            price,
            stock,
            image,
            description,
            specs
        };
        
        if (editingProductId) {
            // Update existing product
            const index = products.findIndex(p => p.id == editingProductId);
            if (index !== -1) {
                products[index] = product;
            }
        } else {
            // Add new product
            products.push(product);
        }
        
        // Sauvegarder l'image si elle a été téléchargée
        if (imagePreview && imagePreview.src.startsWith('data:image')) {
            saveImage(imagePreview.src, imagePreview.dataset.fileName);
        }
        
        // Close modal
        closeModal();
        
        // Refresh products list
        applyFilters();
        renderProducts();
        updatePagination();
        
        // Show success message
        alert(`Product ${editingProductId ? 'updated' : 'added'} successfully!`);
    }
    
    function handleImageUpload(e) {
        const files = e.target.files;
        if (!files || files.length === 0) return;
        
        const previewsContainer = document.getElementById('image-previews');
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // Vérifier si c'est une image
            if (!file.type.startsWith('image/')) {
                alert('Veuillez sélectionner uniquement des fichiers image');
                continue;
            }
            
            const reader = new FileReader();
            
            reader.onload = function(event) {
                const imagePreview = document.createElement('div');
                imagePreview.className = 'image-preview';
                imagePreview.innerHTML = `
                    <img src="${event.target.result}" alt="Product Image" data-file-name="${file.name}">
                    <button type="button" class="remove-image-btn"><i class="fas fa-times"></i></button>
                `;
                
                // Ajouter un événement pour le bouton de suppression
                imagePreview.querySelector('.remove-image-btn').addEventListener('click', function() {
                    imagePreview.remove();
                });
                
                previewsContainer.appendChild(imagePreview);
            };
            
            reader.readAsDataURL(file);
        }
    }
    
    function addSpecificationRow(name = '', value = '') {
        const specsContainer = document.getElementById('specifications-container');
        
        const specRow = document.createElement('div');
        specRow.className = 'spec-row';
        specRow.innerHTML = `
            <input type="text" placeholder="Name (e.g. Display)" value="${name}">
            <input type="text" placeholder="Value (e.g. 6.7-inch OLED)" value="${value}">
            <button type="button" class="remove-spec-btn"><i class="fas fa-times"></i></button>
        `;
        
        // Add event listener to remove button
        specRow.querySelector('.remove-spec-btn').addEventListener('click', function() {
            if (specsContainer.children.length > 1) {
                specRow.remove();
            }
        });
        
        specsContainer.appendChild(specRow);
    }
    
    function deleteProduct(productId) {
        if (confirm('Are you sure you want to delete this product?')) {
            // Remove product from array
            const index = products.findIndex(p => p.id == productId);
            if (index !== -1) {
                products.splice(index, 1);
                
                // Update filtered products
                applyFilters();
                
                // Re-render products
                renderProducts();
                updatePagination();
                
                // Show success message
                alert('Product deleted successfully!');
            }
        }
    }

    // Nouvelle fonction pour sauvegarder l'image
    async function saveImage(dataUrl, fileName) {
        try {
            const response = await fetch('/api/upload-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: dataUrl,
                    fileName: fileName,
                    directory: 'custom-products'
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to upload image');
            }
            
            console.log('Image uploaded successfully');
        } catch (error) {
            console.error('Error uploading image:', error);
            alert('Failed to upload image. The product was saved but you may need to re-upload the image.');
        }
    }
}); 