const express = require('express');
const router = express.Router();
const { query } = require('../config/db');
const { authenticateJWT, isAdmin } = require('../middleware/auth');
const fs = require('fs');
const path = require('path');

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get dashboard statistics
 * @access  Private/Admin
 */
router.get('/dashboard', authenticateJWT, isAdmin, async (req, res) => {
  try {
    // Total sales
    const salesResult = await query('SELECT SUM(total_amount) as total_sales FROM orders WHERE payment_status = $1', ['paid']);
    
    // Total orders
    const ordersResult = await query('SELECT COUNT(*) as total_orders FROM orders');
    
    // Total users
    const usersResult = await query('SELECT COUNT(*) as total_users FROM users');
    
    // Total products
    const productsResult = await query('SELECT COUNT(*) as total_products FROM products');
    
    // Recent orders
    const recentOrdersResult = await query(`
      SELECT o.*, u.username, u.first_name, u.last_name
      FROM orders o 
      JOIN users u ON o.user_id = u.user_id
      ORDER BY o.order_date DESC
      LIMIT 5 
    `);
    
    // Orders by status
    const ordersByStatusResult = await query(`
      SELECT status, COUNT(*) as count
      FROM orders
      GROUP BY status
    `);
    
    // Top selling products
    const topProductsResult = await query(`
      SELECT p.product_id, p.name, SUM(oi.quantity) as total_sold, SUM(oi.quantity * oi.unit_price) as revenue,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM order_items oi
      JOIN products p ON oi.product_id = p.product_id
      JOIN orders o ON oi.order_id = o.order_id
      WHERE o.payment_status = 'paid'
      GROUP BY p.product_id, p.name
      ORDER BY total_sold DESC
      LIMIT 5
    `);
    
    // Low stock products
    const lowStockResult = await query(`
      SELECT product_id, name, stock_quantity
      FROM products
      WHERE stock_quantity <= 10
      ORDER BY stock_quantity ASC
      LIMIT 5
    `);
    
    res.json({
      success: true,
      dashboard: {
        total_sales: parseFloat(salesResult.rows[0]?.total_sales || 0),
        total_orders: parseInt(ordersResult.rows[0].total_orders),
        total_users: parseInt(usersResult.rows[0].total_users),
        total_products: parseInt(productsResult.rows[0].total_products),
        recent_orders: recentOrdersResult.rows,
        orders_by_status: ordersByStatusResult.rows,
        top_products: topProductsResult.rows,
        low_stock_products: lowStockResult.rows
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard data'
    });
  }
});

/**
 * @route   GET /api/admin/sales
 * @desc    Get monthly sales data for chart
 * @access  Private/Admin
 */
router.get('/sales', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        EXTRACT(MONTH FROM order_date) as month_num,
        TO_CHAR(order_date, 'Mon') as month,
        SUM(total_amount) as amount
      FROM orders
      WHERE payment_status = 'paid'
        AND order_date >= NOW() - INTERVAL '6 months'
      GROUP BY month_num, month
      ORDER BY month_num ASC
    `);
    
    res.json({
      success: true,
      sales: result.rows
    });
  } catch (error) {
    console.error('Sales data error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching sales data'
    });
  }
});

/**
 * @route   GET /api/admin/category-sales
 * @desc    Get sales by category for chart
 * @access  Private/Admin
 */
router.get('/category-sales', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT c.name as category, SUM(oi.quantity * oi.unit_price) as sales
      FROM order_items oi
      JOIN products p ON oi.product_id = p.product_id
      JOIN categories c ON p.category_id = c.category_id
      JOIN orders o ON oi.order_id = o.order_id
      WHERE o.payment_status = 'paid'
      GROUP BY c.name
      ORDER BY sales DESC
      LIMIT 5
    `);
    
    res.json({
      success: true,
      category_sales: result.rows
    });
  } catch (error) {
    console.error('Category sales error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching category sales'
    });
  }
});

/**
 * @route   GET /api/admin/recent-orders
 * @desc    Get recent orders for admin dashboard
 * @access  Private/Admin
 */
router.get('/recent-orders', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT o.order_id, o.order_date, o.total_amount, o.status, o.payment_status,
             u.username, u.first_name, u.last_name
      FROM orders o
      JOIN users u ON o.user_id = u.user_id
      ORDER BY o.order_date DESC
      LIMIT 10
    `);
    
    // Format the data for the frontend
    const orders = result.rows.map(order => ({
      order_id: order.order_id,
      customer_name: `${order.first_name} ${order.last_name}`,
      order_date: order.order_date,
      total: order.total_amount,
      status: order.status,
      payment_status: order.payment_status
    }));
    
    res.json({
      success: true,
      recent_orders: orders
    });
  } catch (error) {
    console.error('Recent orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching recent orders'
    });
  }
});

/**
 * @route   GET /api/admin/categories
 * @desc    Get all categories
 * @access  Private/Admin
 */
router.get('/categories', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT c.*, p.name as parent_name,
             (SELECT COUNT(*) FROM products WHERE category_id = c.category_id) as product_count
      FROM categories c
      LEFT JOIN categories p ON c.parent_category_id = p.category_id
      ORDER BY c.name
    `);
    
    res.json({
      success: true,
      categories: result.rows
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching categories'
    });
  }
});

/**
 * @route   POST /api/admin/categories
 * @desc    Create a new category
 * @access  Private/Admin
 */
router.post('/categories', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { name, description, parent_category_id } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
    }
    
    // Check if category with same name exists
    const existingCategory = await query(
      'SELECT * FROM categories WHERE name = $1',
      [name]
    );
    
    if (existingCategory.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Category with this name already exists'
      });
    }
    
    // Create category
    const result = await query(
      'INSERT INTO categories (name, description, parent_category_id) VALUES ($1, $2, $3) RETURNING *',
      [name, description, parent_category_id || null]
    );
    
    res.status(201).json({
      success: true,
      category: result.rows[0]
    });
  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating category'
    });
  }
});

/**
 * @route   PUT /api/admin/categories/:id
 * @desc    Update a category
 * @access  Private/Admin
 */
router.put('/categories/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, parent_category_id } = req.body;
    
    // Check if category exists
    const existingCategory = await query(
      'SELECT * FROM categories WHERE category_id = $1',
      [id]
    );
    
    if (existingCategory.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }
    
    // Check if new name conflicts with existing categories
    if (name) {
      const nameCheck = await query(
        'SELECT * FROM categories WHERE name = $1 AND category_id != $2',
        [name, id]
      );
      
      if (nameCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Category with this name already exists'
        });
      }
    }
    
    // Update category
    const result = await query(
      'UPDATE categories SET name = $1, description = $2, parent_category_id = $3 WHERE category_id = $4 RETURNING *',
      [
        name || existingCategory.rows[0].name,
        description || existingCategory.rows[0].description,
        parent_category_id === null ? null : (parent_category_id || existingCategory.rows[0].parent_category_id),
        id
      ]
    );
    
    res.json({
      success: true,
      category: result.rows[0]
    });
  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating category'
    });
  }
});

/**
 * @route   DELETE /api/admin/categories/:id
 * @desc    Delete a category
 * @access  Private/Admin
 */
router.delete('/categories/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if category exists
    const existingCategory = await query(
      'SELECT * FROM categories WHERE category_id = $1',
      [id]
    );
    
    if (existingCategory.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }
    
    // Check if category has products
    const productsCheck = await query(
      'SELECT COUNT(*) as count FROM products WHERE category_id = $1',
      [id]
    );
    
    if (parseInt(productsCheck.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with associated products'
      });
    }
    
    // Check if category has child categories
    const childrenCheck = await query(
      'SELECT COUNT(*) as count FROM categories WHERE parent_category_id = $1',
      [id]
    );
    
    if (parseInt(childrenCheck.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with child categories'
      });
    }
    
    // Delete category
    await query('DELETE FROM categories WHERE category_id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting category'
    });
  }
});

/**
 * @route   GET /api/admin/brands
 * @desc    Get all brands
 * @access  Private/Admin
 */
router.get('/brands', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT b.*,
             (SELECT COUNT(*) FROM products WHERE brand_id = b.brand_id) as product_count
      FROM brands b
      ORDER BY b.name
    `);
    
    res.json({
      success: true,
      brands: result.rows
    });
  } catch (error) {
    console.error('Get brands error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching brands'
    });
  }
});

/**
 * @route   POST /api/admin/brands
 * @desc    Create a new brand
 * @access  Private/Admin
 */
router.post('/brands', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { name, description, logo_url } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Brand name is required'
      });
    }
    
    // Check if brand with same name exists
    const existingBrand = await query(
      'SELECT * FROM brands WHERE name = $1',
      [name]
    );
    
    if (existingBrand.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Brand with this name already exists'
      });
    }
    
    // Create brand
    const result = await query(
      'INSERT INTO brands (name, description, logo_url) VALUES ($1, $2, $3) RETURNING *',
      [name, description, logo_url]
    );
    
    res.status(201).json({
      success: true,
      brand: result.rows[0]
    });
  } catch (error) {
    console.error('Create brand error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating brand'
    });
  }
});

/**
 * @route   PUT /api/admin/brands/:id
 * @desc    Update a brand
 * @access  Private/Admin
 */
router.put('/brands/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, logo_url } = req.body;
    
    // Check if brand exists
    const existingBrand = await query(
      'SELECT * FROM brands WHERE brand_id = $1',
      [id]
    );
    
    if (existingBrand.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    // Check if new name conflicts with existing brands
    if (name) {
      const nameCheck = await query(
        'SELECT * FROM brands WHERE name = $1 AND brand_id != $2',
        [name, id]
      );
      
      if (nameCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Brand with this name already exists'
        });
      }
    }
    
    // Update brand
    const result = await query(
      'UPDATE brands SET name = $1, description = $2, logo_url = $3 WHERE brand_id = $4 RETURNING *',
      [
        name || existingBrand.rows[0].name,
        description || existingBrand.rows[0].description,
        logo_url || existingBrand.rows[0].logo_url,
        id
      ]
    );
    
    res.json({
      success: true,
      brand: result.rows[0]
    });
  } catch (error) {
    console.error('Update brand error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating brand'
    });
  }
});

/**
 * @route   DELETE /api/admin/brands/:id
 * @desc    Delete a brand
 * @access  Private/Admin
 */
router.delete('/brands/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if brand exists
    const existingBrand = await query(
      'SELECT * FROM brands WHERE brand_id = $1',
      [id]
    );
    
    if (existingBrand.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    // Check if brand has products
    const productsCheck = await query(
      'SELECT COUNT(*) as count FROM products WHERE brand_id = $1',
      [id]
    );
    
    if (parseInt(productsCheck.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete brand with associated products'
      });
    }
    
    // Delete brand
    await query('DELETE FROM brands WHERE brand_id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Brand deleted successfully'
    });
  } catch (error) {
    console.error('Delete brand error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting brand'
    });
  }
});

/**
 * @route   GET /api/admin/promotions
 * @desc    Get all promotions
 * @access  Private/Admin
 */
router.get('/promotions', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT p.*,
             (SELECT COUNT(*) FROM product_promotions WHERE promotion_id = p.promotion_id) as product_count
      FROM promotions p
      ORDER BY p.start_date DESC
    `);
    
    res.json({
      success: true,
      promotions: result.rows
    });
  } catch (error) {
    console.error('Get promotions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching promotions'
    });
  }
});

/**
 * @route   POST /api/admin/promotions
 * @desc    Create a new promotion
 * @access  Private/Admin
 */
router.post('/promotions', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { name, description, discount_percentage, start_date, end_date, is_active, product_ids } = req.body;
    
    if (!name || !discount_percentage || !start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }
    
    // Create promotion
    const result = await query(
      `INSERT INTO promotions 
       (name, description, discount_percentage, start_date, end_date, is_active) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING *`,
      [name, description, discount_percentage, start_date, end_date, is_active !== undefined ? is_active : true]
    );
    
    const promotion = result.rows[0];
    
    // Add products to promotion if provided
    if (product_ids && product_ids.length > 0) {
      for (const productId of product_ids) {
        await query(
          'INSERT INTO product_promotions (product_id, promotion_id) VALUES ($1, $2)',
          [productId, promotion.promotion_id]
        );
      }
    }
    
    res.status(201).json({
      success: true,
      promotion
    });
  } catch (error) {
    console.error('Create promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating promotion'
    });
  }
});

/**
 * @route   PUT /api/admin/promotions/:id
 * @desc    Update a promotion
 * @access  Private/Admin
 */
router.put('/promotions/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, discount_percentage, start_date, end_date, is_active, product_ids } = req.body;
    
    // Check if promotion exists
    const existingPromotion = await query(
      'SELECT * FROM promotions WHERE promotion_id = $1',
      [id]
    );
    
    if (existingPromotion.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }
    
    // Update promotion
    const result = await query(
      `UPDATE promotions 
       SET name = $1, description = $2, discount_percentage = $3, 
           start_date = $4, end_date = $5, is_active = $6
       WHERE promotion_id = $7 
       RETURNING *`,
      [
        name || existingPromotion.rows[0].name,
        description || existingPromotion.rows[0].description,
        discount_percentage || existingPromotion.rows[0].discount_percentage,
        start_date || existingPromotion.rows[0].start_date,
        end_date || existingPromotion.rows[0].end_date,
        is_active !== undefined ? is_active : existingPromotion.rows[0].is_active,
        id
      ]
    );
    
    // Update product associations if provided
    if (product_ids) {
      // Remove existing associations
      await query('DELETE FROM product_promotions WHERE promotion_id = $1', [id]);
      
      // Add new associations
      if (product_ids.length > 0) {
        for (const productId of product_ids) {
          await query(
            'INSERT INTO product_promotions (product_id, promotion_id) VALUES ($1, $2)',
            [productId, id]
          );
        }
      }
    }
    
    res.json({
      success: true,
      promotion: result.rows[0]
    });
  } catch (error) {
    console.error('Update promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating promotion'
    });
  }
});

/**
 * @route   DELETE /api/admin/promotions/:id
 * @desc    Delete a promotion
 * @access  Private/Admin
 */
router.delete('/promotions/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if promotion exists
    const existingPromotion = await query(
      'SELECT * FROM promotions WHERE promotion_id = $1',
      [id]
    );
    
    if (existingPromotion.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }
    
    // Delete promotion (cascades to product_promotions)
    await query('DELETE FROM promotions WHERE promotion_id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Promotion deleted successfully'
    });
  } catch (error) {
    console.error('Delete promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting promotion'
    });
  }
});

// Route pour l'upload d'images
router.post('/upload-image', async (req, res) => {
    try {
        const { image, fileName, directory } = req.body;

        // Vérifier que les données nécessaires sont présentes
        if (!image || !fileName || !directory) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Extraire les données de l'image base64
        const base64Data = image.replace(/^data:image\/\w+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');

        // Créer le chemin du dossier de destination
        const uploadDir = path.join(__dirname, '../../frontend/images/', directory);

        // Créer le dossier s'il n'existe pas
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Chemin complet du fichier
        const filePath = path.join(uploadDir, fileName);

        // Sauvegarder l'image
        fs.writeFileSync(filePath, buffer);

        res.json({ success: true, path: `/images/${directory}/${fileName}` });
    } catch (error) {
        console.error('Error uploading image:', error);
        res.status(500).json({ error: 'Failed to upload image' });
    }
});

module.exports = router; 