// Products Page JavaScript
document.addEventListener('DOMContentLoaded', async () => {
    // DOM Elements
    const productsGrid = document.getElementById('products-grid');
    const productsTotal = document.getElementById('products-total');
    const categoryFilters = document.getElementById('category-filters');
    const brandFilters = document.getElementById('brand-filters');
    const priceSlider = document.getElementById('price-slider');
    const priceValue = document.getElementById('price-value');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const sortBySelect = document.getElementById('sort-by');
    const paginationNumbers = document.getElementById('pagination-numbers');
    const paginationBtns = document.querySelectorAll('.pagination-btn');

    // State
    let products = [];
    let filteredProducts = [];
    let currentPage = 1;
    let productsPerPage = 12;
    let filters = {
        categories: [],
        brands: [],
        maxPrice: 200000,
        sortBy: 'newest'
    };

    // Initialize with dependency management
    try {
        // Wait for dependencies if available
        if (window.DependencyManager) {
            await window.DependencyManager.waitFor(['DOM', 'RaoufStoreData', 'CreateProductCard'], 10000);
        }

        await init();
    } catch (error) {
        console.error('❌ Error initializing products page:', error);
        // Fallback initialization
        await init();
    }

    // Initialize the page
    async function init() {
        // Load categories and brands first
        await loadCategoriesAndBrands();

        // Update price slider value
        updatePriceValue();

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        // Set initial filters from URL parameters
        if (urlParams.has('category')) {
            const categoryIds = urlParams.get('category').split(',');
            categoryIds.forEach(id => {
                const checkbox = categoryFilters.querySelector(`input[value="${id}"]`);
                if (checkbox) checkbox.checked = true;
                filters.categories.push(id);
            });
        }
        
        if (urlParams.has('brand')) {
            const brandIds = urlParams.get('brand').split(',');
            brandIds.forEach(id => {
                const checkbox = brandFilters.querySelector(`input[value="${id}"]`);
                if (checkbox) checkbox.checked = true;
                filters.brands.push(id);
            });
        }
        
        if (urlParams.has('price')) {
            const price = parseInt(urlParams.get('price'));
            if (!isNaN(price)) {
                priceSlider.value = price;
                filters.maxPrice = price;
                updatePriceValue();
            }
        }
        
        if (urlParams.has('sort')) {
            const sort = urlParams.get('sort');
            sortBySelect.value = sort;
            filters.sortBy = sort;
        }
        
        if (urlParams.has('page')) {
            const page = parseInt(urlParams.get('page'));
            if (!isNaN(page)) {
                currentPage = page;
            }
        }
        
        // Add event listeners
        priceSlider.addEventListener('input', updatePriceValue);
        applyFiltersBtn.addEventListener('click', applyFilters);
        clearFiltersBtn.addEventListener('click', clearFilters);
        sortBySelect.addEventListener('change', handleSort);
        paginationBtns.forEach(btn => {
            btn.addEventListener('click', handlePagination);
        });
        
        // Fetch products
        await fetchProducts();
        
        // Apply initial filters
        applyFilters();
    }

    // Load categories and brands from data manager
    async function loadCategoriesAndBrands() {
        try {
            console.log('📱 Loading categories and brands...');

            // Use dependency manager if available
            if (window.DependencyManager) {
                await window.DependencyManager.waitFor(['RaoufStoreData'], 5000);
            } else {
                // Fallback: Wait for data manager to be ready
                let attempts = 0;
                while ((!window.RaoufStoreData || !window.RaoufStoreData.initialized) && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
            }

            if (window.RaoufStoreData && window.RaoufStoreData.initialized) {
                // Load categories
                if (window.RaoufStoreData.categories && window.RaoufStoreData.categories.length > 0) {
                    loadCategoriesIntoFilters(window.RaoufStoreData.categories);
                    console.log('✅ Categories loaded:', window.RaoufStoreData.categories.length);
                } else {
                    console.log('⚠️ No categories found, using default');
                    loadDefaultCategories();
                }

                // Load brands
                if (window.RaoufStoreData.brands && window.RaoufStoreData.brands.length > 0) {
                    loadBrandsIntoFilters(window.RaoufStoreData.brands);
                    console.log('✅ Brands loaded:', window.RaoufStoreData.brands.length);
                } else {
                    console.log('⚠️ No brands found, using default');
                    loadDefaultBrands();
                }
            } else {
                console.log('⚠️ Data manager not ready, using default categories and brands');
                loadDefaultCategories();
                loadDefaultBrands();
            }
        } catch (error) {
            console.error('❌ Error loading categories and brands:', error);
            loadDefaultCategories();
            loadDefaultBrands();
        }
    }

    // Load categories into filters
    function loadCategoriesIntoFilters(categories) {
        if (!categoryFilters) return;

        categoryFilters.innerHTML = '';
        categories.forEach(category => {
            const li = document.createElement('li');
            li.innerHTML = `
                <label>
                    <input type="checkbox" value="${category.id}" name="category"> ${category.name}
                </label>
            `;
            categoryFilters.appendChild(li);
        });
    }

    // Load brands into filters
    function loadBrandsIntoFilters(brands) {
        if (!brandFilters) return;

        brandFilters.innerHTML = '';
        brands.forEach(brand => {
            const li = document.createElement('li');
            li.innerHTML = `
                <label>
                    <input type="checkbox" value="${brand.id}" name="brand"> ${brand.name}
                </label>
            `;
            brandFilters.appendChild(li);
        });
    }

    // Load default categories if data manager fails
    function loadDefaultCategories() {
        const defaultCategories = [
            { id: 1, name: 'iPhone' },
            { id: 2, name: 'Samsung' },
            { id: 3, name: 'Google' },
            { id: 4, name: 'OnePlus' },
            { id: 5, name: 'Xiaomi' },
            { id: 6, name: 'Tablets' },
            { id: 7, name: 'Accessories' },
            { id: 8, name: 'Wearables' }
        ];
        loadCategoriesIntoFilters(defaultCategories);
    }

    // Load default brands if data manager fails
    function loadDefaultBrands() {
        const defaultBrands = [
            { id: 1, name: 'Apple' },
            { id: 2, name: 'Samsung' },
            { id: 3, name: 'Google' },
            { id: 4, name: 'OnePlus' },
            { id: 5, name: 'Xiaomi' },
            { id: 6, name: 'Huawei' },
            { id: 7, name: 'Sony' },
            { id: 8, name: 'LG' }
        ];
        loadBrandsIntoFilters(defaultBrands);
    }

    // Update price slider value
    function updatePriceValue() {
        const value = priceSlider.value;
        if (value >= 200000) {
            priceValue.textContent = '200,000+ DZD';
        } else {
            priceValue.textContent = `${parseInt(value).toLocaleString()} DZD`;
        }
        filters.maxPrice = parseInt(value);
    }

    // Fetch products from API
    async function fetchProducts() {
        try {
            const response = await fetch(`${API_URL}/products?limit=100`);
            const data = await response.json();
            
            if (Array.isArray(data)) {
                products = data;
                return;
            }
            
            // Fallback to sample data if API fails or returns unexpected format
            generateSampleProducts();
            
        } catch (error) {
            console.error('Error fetching products:', error);
            // Fallback to sample data
            generateSampleProducts();
        }
    }

    // Generate sample products for fallback
    function generateSampleProducts() {
        const sampleBrands = [
            { id: 1, name: 'Apple' },
            { id: 2, name: 'Samsung' },
            { id: 3, name: 'Google' },
            { id: 4, name: 'OnePlus' },
            { id: 5, name: 'Xiaomi' }
        ];
        
        const sampleCategories = [
            { id: 1, name: 'Smartphones' },
            { id: 2, name: 'Tablets' },
            { id: 3, name: 'Accessories' }
        ];
        
        // Product image mapping with real photos
        const productImages = {
            1: { image_url: 'images/real-products/iphone13_main.jpg' },
            2: { image_url: 'images/real-products/samsung_s21_main.jpg' },
            3: { image_url: 'images/real-products/pixel6_main.jpg' },
            4: { image_url: 'images/real-products/oneplus9_main.jpg' },
            5: { image_url: 'images/real-products/xiaomi12_main.jpg' },
            6: { image_url: 'images/real-products/ipad_pro_main.jpg' },
            7: { image_url: 'images/real-products/galaxy_tab_main.jpg' },
            8: { image_url: 'images/real-products/premium_case_main.jpg' },
            9: { image_url: 'images/real-products/fast_charger_main.jpg' },
            10: { image_url: 'images/real-products/screen_protector_main.jpg' }
        };
        
        const sampleProducts = [
            {
                product_id: 1,
                name: 'iPhone 13 Pro Max',
                description: 'Apple\'s flagship smartphone with a 6.7-inch Super Retina XDR display, A15 Bionic chip, and Pro camera system',
                price: 147000,
                brand_id: 1,
                category_id: 1,
                images: [productImages[1]]
            },
            {
                product_id: 2,
                name: 'Samsung Galaxy S21 Ultra',
                description: 'Samsung\'s premium flagship with 108MP camera, S Pen support, and 6.8-inch Dynamic AMOLED display',
                price: 160000,
                brand_id: 2,
                category_id: 1,
                images: [productImages[2]]
            },
            {
                product_id: 3,
                name: 'Google Pixel 6 Pro',
                description: 'Google\'s flagship with Tensor chip, 6.7-inch LTPO OLED display, and advanced camera system',
                price: 120000,
                brand_id: 3,
                category_id: 1,
                images: [productImages[3]]
            },
            {
                product_id: 4,
                name: 'OnePlus 9 Pro',
                description: 'OnePlus flagship with Hasselblad camera, 120Hz fluid display, and Snapdragon 888',
                price: 129000,
                brand_id: 4,
                category_id: 1,
                images: [productImages[4]]
            },
            {
                product_id: 5,
                name: 'Xiaomi Mi 11 Ultra',
                description: 'Xiaomi\'s premium device with 50MP camera, secondary rear display, and Snapdragon 888',
                price: 160000,
                brand_id: 5,
                category_id: 1,
                images: [productImages[5]]
            },
            {
                product_id: 6,
                name: 'iPad Pro 12.9 (2021)',
                description: 'Apple\'s premium tablet with M1 chip, Liquid Retina XDR display, and Thunderbolt support',
                price: 147000,
                brand_id: 1,
                category_id: 2,
                images: [productImages[6]]
            },
            {
                product_id: 7,
                name: 'Samsung Galaxy Tab S7+',
                description: 'Samsung\'s flagship tablet with 12.4-inch Super AMOLED display, S Pen, and Snapdragon 865+',
                price: 113000,
                brand_id: 2,
                category_id: 2,
                images: [productImages[7]]
            },
            {
                product_id: 8,
                name: 'Premium Leather Case',
                description: 'Genuine leather case with card slots and premium finish',
                price: 6700,
                brand_id: 1,
                category_id: 3,
                images: [productImages[8]]
            },
            {
                product_id: 9,
                name: '25W Fast Charging Adapter',
                description: 'USB-C power adapter for quick charging with Power Delivery technology',
                price: 4000,
                brand_id: 2,
                category_id: 3,
                images: [productImages[9]]
            },
            {
                product_id: 10,
                name: 'Tempered Glass Screen Protector',
                description: '9H hardness tempered glass with oleophobic coating and easy installation',
                price: 2700,
                brand_id: 2,
                category_id: 3,
                images: [productImages[10]]
            }
        ];
        
        // Set products
        products = sampleProducts;
    }

    // Apply filters
    function applyFilters() {
        // Get selected categories
        filters.categories = [];
        const categoryCheckboxes = categoryFilters.querySelectorAll('input[type="checkbox"]:checked');
        categoryCheckboxes.forEach(checkbox => {
            filters.categories.push(checkbox.value);
        });
        
        // Get selected brands
        filters.brands = [];
        const brandCheckboxes = brandFilters.querySelectorAll('input[type="checkbox"]:checked');
        brandCheckboxes.forEach(checkbox => {
            filters.brands.push(checkbox.value);
        });
        
        // Get max price
        filters.maxPrice = parseInt(priceSlider.value);
        
        // Get sort order
        filters.sortBy = sortBySelect.value;
        
        // Filter products
        filterProducts();
        
        // Update URL parameters
        updateUrlParams();
    }

    // Clear all filters
    function clearFilters() {
        // Clear checkboxes
        const checkboxes = document.querySelectorAll('.filter-list input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // Reset price slider
        priceSlider.value = 2000;
        updatePriceValue();
        
        // Reset sort
        sortBySelect.value = 'newest';
        
        // Reset page
        currentPage = 1;
        
        // Apply empty filters
        applyFilters();
    }

    // Handle sort change
    function handleSort() {
        filters.sortBy = sortBySelect.value;
        currentPage = 1;
        applyFilters();
    }

    // Handle pagination
    function handlePagination(e) {
        const direction = e.currentTarget.dataset.page;
        
        if (direction === 'prev' && currentPage > 1) {
            currentPage--;
        } else if (direction === 'next' && currentPage < Math.ceil(filteredProducts.length / productsPerPage)) {
            currentPage++;
        } else if (!isNaN(parseInt(direction))) {
            currentPage = parseInt(direction);
        }
        
        renderProducts();
        updateUrlParams();
        scrollToTop();
    }

    // Scroll to top of products
    function scrollToTop() {
        window.scrollTo({
            top: document.querySelector('.products-section').offsetTop - 100,
            behavior: 'smooth'
        });
    }

    // Filter products based on selected filters
    function filterProducts() {
        filteredProducts = products.filter(product => {
            // Filter by category
            if (filters.categories.length > 0 && !filters.categories.includes(product.category_id.toString())) {
                return false;
            }
            
            // Filter by brand
            if (filters.brands.length > 0 && !filters.brands.includes(product.brand_id.toString())) {
                return false;
            }
            
            // Filter by price
            if (product.price > filters.maxPrice) {
                return false;
            }
            
            return true;
        });
        
        // Sort products
        sortProducts();
        
        // Reset to page 1 when filters change
        currentPage = 1;
        
        // Render the filtered products
        renderProducts();
    }

    // Sort products based on selected sort option
    function sortProducts() {
        switch (filters.sortBy) {
            case 'price_asc':
                filteredProducts.sort((a, b) => a.price - b.price);
                break;
            case 'price_desc':
                filteredProducts.sort((a, b) => b.price - a.price);
                break;
            case 'name_asc':
                filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name_desc':
                filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'newest':
            default:
                // Assuming product_id correlates with newness
                filteredProducts.sort((a, b) => b.product_id - a.product_id);
                break;
        }
    }

    // Render products to the DOM
    function renderProducts() {
        // Update total count
        productsTotal.textContent = filteredProducts.length;
        
        // Calculate pagination
        const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
        const startIndex = (currentPage - 1) * productsPerPage;
        const endIndex = startIndex + productsPerPage;
        const currentProducts = filteredProducts.slice(startIndex, endIndex);
        
        // Clear products grid
        productsGrid.innerHTML = '';
        
        // Render products
        if (currentProducts.length === 0) {
            productsGrid.innerHTML = '<div class="no-products">No products found matching your criteria. Try adjusting your filters.</div>';
        } else {
            currentProducts.forEach(product => {
                const productCard = createProductCard(product);
                productsGrid.appendChild(productCard);
            });
            
            // Note: Add to cart event listeners are handled globally in app.js
        }
        
        // Update pagination
        updatePagination(totalPages);
    }

    // Update pagination controls
    function updatePagination(totalPages) {
        // Clear pagination numbers
        paginationNumbers.innerHTML = '';
        
        // Add pagination numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = 'pagination-number' + (i === currentPage ? ' active' : '');
            pageButton.textContent = i;
            pageButton.dataset.page = i;
            pageButton.addEventListener('click', handlePagination);
            paginationNumbers.appendChild(pageButton);
        }
        
        // Hide pagination if only one page
        document.getElementById('pagination').style.display = totalPages <= 1 ? 'none' : 'flex';
        
        // Disable prev/next buttons if at first/last page
        const prevBtn = document.querySelector('.pagination-btn[data-page="prev"]');
        const nextBtn = document.querySelector('.pagination-btn[data-page="next"]');
        
        prevBtn.disabled = currentPage === 1;
        nextBtn.disabled = currentPage === totalPages || totalPages === 0;
        
        prevBtn.style.opacity = currentPage === 1 ? '0.5' : '1';
        nextBtn.style.opacity = (currentPage === totalPages || totalPages === 0) ? '0.5' : '1';
    }

    // Update URL parameters
    function updateUrlParams() {
        const urlParams = new URLSearchParams();
        
        // Add categories to URL
        if (filters.categories.length > 0) {
            urlParams.set('category', filters.categories.join(','));
        }
        
        // Add brands to URL
        if (filters.brands.length > 0) {
            urlParams.set('brand', filters.brands.join(','));
        }
        
        // Add price to URL if not default
        if (filters.maxPrice < 2000) {
            urlParams.set('price', filters.maxPrice);
        }
        
        // Add sort to URL if not default
        if (filters.sortBy !== 'newest') {
            urlParams.set('sort', filters.sortBy);
        }
        
        // Add page to URL if not first page
        if (currentPage > 1) {
            urlParams.set('page', currentPage);
        }
        
        // Update URL without reload
        const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
        window.history.pushState({ path: newUrl }, '', newUrl);
    }
}); 