const express = require('express');
const cors = require('cors');

// Initialize express app
const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Log requests for development
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  next();
});

// Health check route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Root route
app.get('/api', (req, res) => {
  res.json({
    message: 'Welcome to PhoneHub E-Commerce API',
    status: 'Running without database'
  });
});

// Mock products route
app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    products: [
      {
        product_id: 1,
        name: 'iPhone 13 Pro Max',
        price: 1099.99,
        description: 'Apple\'s flagship smartphone',
        brand_name: 'Apple',
        category_name: 'Smartphones'
      },
      {
        product_id: 2,
        name: 'Samsung Galaxy S21',
        price: 799.99,
        description: 'Samsung\'s flagship smartphone',
        brand_name: 'Samsung',
        category_name: 'Smartphones'
      }
    ]
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API accessible at http://localhost:${PORT}/api`);
  console.log('Running in simple mode without database');
});

module.exports = app;
