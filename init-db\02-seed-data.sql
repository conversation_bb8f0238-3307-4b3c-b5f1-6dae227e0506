-- Seed data for ecommerce_phones database

-- Users data
INSERT INTO users (username, email, password_hash, first_name, last_name, phone_number, address, role)
VALUES
  ('admin', '<EMAIL>', '$2b$10$rNC7O.EnWFyPbWAKuGvs/uRwdDJ3xQNOeF1gXUX3soLQYyucnnU3m', 'Admin', 'User', '************', '123 Admin St, Tech City, TC 12345', 'admin'),
  ('johndoe', '<EMAIL>', '$2b$10$YourHashedPasswordHere', '<PERSON>', 'Do<PERSON>', '************', '456 Main St, Anytown, AT 67890', 'customer'),
  ('janedoe', '<EMAIL>', '$2b$10$YourHashedPasswordHere', 'Jane', 'Doe', '************', '789 Oak Ave, Somecity, SC 54321', 'customer'),
  ('mikebrown', '<EMAIL>', '$2b$10$YourHashedPasswordHere', '<PERSON>', '<PERSON>', '************', '101 Pine Rd, Anothercity, AC 13579', 'customer'),
  ('sarahsmith', '<EMAIL>', '$2b$10$YourHashedPasswordHere', 'Sarah', 'Smith', '************', '202 Maple Dr, Lastcity, LC 24680', 'customer');

-- Categories data (Parent categories first)
INSERT INTO categories (name, description, parent_category_id)
VALUES 
  ('Smartphones', 'All smartphone devices', NULL),
  ('Tablets', 'Tablet devices of all sizes', NULL),
  ('Accessories', 'Phone and tablet accessories', NULL),
  ('Android Phones', 'Smartphones running Android OS', 1),
  ('iOS Phones', 'Apple iPhones running iOS', 1),
  ('Windows Phones', 'Smartphones running Windows Mobile', 1),
  ('Phone Cases', 'Protective cases for smartphones', 3),
  ('Chargers', 'Charging devices and cables', 3),
  ('Screen Protectors', 'Protective films and glass for screens', 3),
  ('Bluetooth Headphones', 'Wireless headphones using Bluetooth', 3);

-- Brands data
INSERT INTO brands (name, description, logo_url)
VALUES
  ('Apple', 'American technology company that designs, develops, and sells consumer electronics', 'https://example.com/logos/apple.png'),
  ('Samsung', 'South Korean multinational conglomerate', 'https://example.com/logos/samsung.png'),
  ('Google', 'American technology company specializing in Internet-related services and products', 'https://example.com/logos/google.png'),
  ('OnePlus', 'Chinese smartphone manufacturer', 'https://example.com/logos/oneplus.png'),
  ('Xiaomi', 'Chinese electronics company', 'https://example.com/logos/xiaomi.png'),
  ('Huawei', 'Chinese multinational technology company', 'https://example.com/logos/huawei.png'),
  ('Sony', 'Japanese multinational conglomerate corporation', 'https://example.com/logos/sony.png'),
  ('Nokia', 'Finnish telecommunications company', 'https://example.com/logos/nokia.png'),
  ('Motorola', 'American telecommunications company', 'https://example.com/logos/motorola.png'),
  ('LG', 'South Korean multinational electronics company', 'https://example.com/logos/lg.png');

-- Products data
INSERT INTO products (name, description, price, stock_quantity, brand_id, category_id, release_date, specifications)
VALUES
  ('iPhone 13 Pro Max', 'Apple''s flagship smartphone with a 6.7-inch display', 1099.99, 50, 1, 5, '2021-09-24', 
   '{"display": "6.7-inch OLED", "processor": "A15 Bionic", "storage": "128GB", "camera": "12MP triple", "battery": "4352mAh", "ram": "6GB", "os": "iOS 15"}'),
  
  ('Samsung Galaxy S21', 'Samsung''s flagship smartphone with a 6.2-inch display', 799.99, 35, 2, 4, '2021-01-29', 
   '{"display": "6.2-inch AMOLED", "processor": "Snapdragon 888", "storage": "128GB", "camera": "12MP triple", "battery": "4000mAh", "ram": "8GB", "os": "Android 11"}'),
  
  ('Google Pixel 6', 'Google''s flagship smartphone with a 6.4-inch display', 699.00, 25, 3, 4, '2021-10-28', 
   '{"display": "6.4-inch OLED", "processor": "Google Tensor", "storage": "128GB", "camera": "50MP+12MP", "battery": "4614mAh", "ram": "8GB", "os": "Android 12"}'),
  
  ('OnePlus 9 Pro', 'OnePlus flagship smartphone with a 6.7-inch display', 969.00, 20, 4, 4, '2021-03-23', 
   '{"display": "6.7-inch AMOLED", "processor": "Snapdragon 888", "storage": "256GB", "camera": "48MP quad", "battery": "4500mAh", "ram": "12GB", "os": "OxygenOS 11"}'),
  
  ('Xiaomi Mi 11', 'Xiaomi flagship smartphone with a 6.81-inch display', 749.99, 30, 5, 4, '2021-02-08', 
   '{"display": "6.81-inch AMOLED", "processor": "Snapdragon 888", "storage": "128GB", "camera": "108MP triple", "battery": "4600mAh", "ram": "8GB", "os": "MIUI 12"}'),
  
  ('iPad Pro 12.9 (2021)', 'Apple''s flagship tablet with a 12.9-inch display', 1099.99, 15, 1, 2, '2021-05-21', 
   '{"display": "12.9-inch Liquid Retina XDR", "processor": "M1", "storage": "128GB", "camera": "12MP", "battery": "10000mAh", "ram": "8GB", "os": "iPadOS 15"}'),
  
  ('Samsung Galaxy Tab S7+', 'Samsung''s flagship tablet with a 12.4-inch display', 849.99, 10, 2, 2, '2020-08-21', 
   '{"display": "12.4-inch AMOLED", "processor": "Snapdragon 865+", "storage": "128GB", "camera": "13MP+5MP", "battery": "10090mAh", "ram": "6GB", "os": "Android 10"}'),
  
  ('Genuine Leather Case for iPhone 13', 'Premium leather case for iPhone 13', 49.99, 100, 1, 7, '2021-09-24', 
   '{"material": "Genuine leather", "color": "Black", "compatibility": "iPhone 13", "features": "Card slots, Kickstand"}'),
  
  ('Fast Charging Power Adapter', 'USB-C power adapter for quick charging', 29.99, 200, 2, 8, '2021-01-01', 
   '{"input": "100-240V", "output": "25W", "type": "USB-C", "features": "Fast Charging, Compact design"}'),
  
  ('Tempered Glass Screen Protector', 'Durable screen protector for smartphones', 19.99, 500, 2, 9, '2021-01-01', 
   '{"material": "Tempered glass", "thickness": "0.3mm", "hardness": "9H", "features": "Anti-fingerprint, Bubble-free"}');

-- Product images
INSERT INTO product_images (product_id, image_url, is_primary)
VALUES
  (1, 'https://example.com/images/iphone13promax-1.jpg', true),
  (1, 'https://example.com/images/iphone13promax-2.jpg', false),
  (2, 'https://example.com/images/galaxys21-1.jpg', true),
  (2, 'https://example.com/images/galaxys21-2.jpg', false),
  (3, 'https://example.com/images/pixel6-1.jpg', true),
  (3, 'https://example.com/images/pixel6-2.jpg', false),
  (4, 'https://example.com/images/oneplus9pro-1.jpg', true),
  (5, 'https://example.com/images/mi11-1.jpg', true),
  (6, 'https://example.com/images/ipadpro-1.jpg', true),
  (7, 'https://example.com/images/tabs7plus-1.jpg', true),
  (8, 'https://example.com/images/iphonecase-1.jpg', true),
  (9, 'https://example.com/images/poweradapter-1.jpg', true),
  (10, 'https://example.com/images/screenprotector-1.jpg', true);

-- Orders data
INSERT INTO orders (user_id, order_date, total_amount, status, shipping_address, payment_status)
VALUES
  (2, NOW() - INTERVAL '2 days', 1149.98, 'pending', '456 Main St, Anytown, AT 67890', 'pending'),
  (3, NOW() - INTERVAL '5 days', 819.98, 'processing', '789 Oak Ave, Somecity, SC 54321', 'paid'),
  (4, NOW() - INTERVAL '10 days', 699.00, 'shipped', '101 Pine Rd, Anothercity, AC 13579', 'paid'),
  (5, NOW() - INTERVAL '15 days', 969.00, 'delivered', '202 Maple Dr, Lastcity, LC 24680', 'paid'),
  (2, NOW() - INTERVAL '30 days', 749.99, 'cancelled', '456 Main St, Anytown, AT 67890', 'failed');

-- Order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price)
VALUES
  (1, 1, 1, 1099.99),
  (1, 8, 1, 49.99),
  (2, 2, 1, 799.99),
  (2, 9, 1, 19.99),
  (3, 3, 1, 699.00),
  (4, 4, 1, 969.00),
  (5, 5, 1, 749.99);

-- Reviews data
INSERT INTO reviews (product_id, user_id, rating, comment, created_at)
VALUES
  (1, 3, 5, 'Best phone I''ve ever used. The camera is amazing!', NOW() - INTERVAL '10 days'),
  (1, 4, 4, 'Great phone but a bit expensive.', NOW() - INTERVAL '15 days'),
  (2, 2, 5, 'Samsung never disappoints. Excellent performance.', NOW() - INTERVAL '20 days'),
  (3, 5, 4, 'Clean Android experience and great camera.', NOW() - INTERVAL '5 days'),
  (4, 3, 5, 'OnePlus has really stepped up their game with this model.', NOW() - INTERVAL '25 days'),
  (5, 2, 4, 'Great value for the price, but MIUI has some quirks.', NOW() - INTERVAL '30 days');

-- Promotions data
INSERT INTO promotions (name, description, discount_percentage, start_date, end_date, is_active)
VALUES
  ('Summer Sale', 'Hot discounts on all smartphones', 15.00, NOW() - INTERVAL '10 days', NOW() + INTERVAL '20 days', true),
  ('Back to School', 'Special deals for students', 10.00, NOW() - INTERVAL '15 days', NOW() + INTERVAL '15 days', true),
  ('Holiday Special', 'End of year discount event', 20.00, NOW() + INTERVAL '30 days', NOW() + INTERVAL '60 days', false),
  ('Flash Sale', '24-hour special discounts', 25.00, NOW() - INTERVAL '5 days', NOW() - INTERVAL '4 days', false),
  ('Brand Spotlight: Samsung', 'Special discounts on all Samsung products', 12.50, NOW(), NOW() + INTERVAL '7 days', true);

-- Product promotions
INSERT INTO product_promotions (product_id, promotion_id)
VALUES
  (1, 1),
  (2, 1),
  (3, 1),
  (4, 1),
  (5, 1),
  (2, 5),
  (7, 5),
  (2, 2),
  (3, 2),
  (6, 2),
  (7, 2);

-- Shopping carts
INSERT INTO shopping_carts (user_id, created_at)
VALUES
  (2, NOW() - INTERVAL '1 day'),
  (3, NOW() - INTERVAL '2 days'),
  (4, NOW() - INTERVAL '3 hours');

-- Cart items
INSERT INTO cart_items (cart_id, product_id, quantity, added_at)
VALUES
  (1, 3, 1, NOW() - INTERVAL '1 day'),
  (1, 9, 2, NOW() - INTERVAL '1 day'),
  (2, 5, 1, NOW() - INTERVAL '2 days'),
  (2, 8, 1, NOW() - INTERVAL '2 days'),
  (3, 1, 1, NOW() - INTERVAL '3 hours');

-- Add specifications for products
INSERT INTO specifications (product_id, name, value)
VALUES
  (1, 'Screen Size', '6.7 inches'),
  (1, 'Resolution', '2778 x 1284 pixels'),
  (1, 'CPU', 'A15 Bionic'),
  (1, 'RAM', '6GB'),
  (1, 'Storage', '128GB'),
  (1, 'Main Camera', '12MP + 12MP + 12MP'),
  (1, 'Selfie Camera', '12MP'),
  (1, 'Battery', '4352 mAh'),
  
  (2, 'Screen Size', '6.2 inches'),
  (2, 'Resolution', '2400 x 1080 pixels'),
  (2, 'CPU', 'Snapdragon 888'),
  (2, 'RAM', '8GB'),
  (2, 'Storage', '128GB'),
  (2, 'Main Camera', '12MP + 12MP + 64MP'),
  (2, 'Selfie Camera', '10MP'),
  (2, 'Battery', '4000 mAh'),
  
  (3, 'Screen Size', '6.4 inches'),
  (3, 'Resolution', '2400 x 1080 pixels'),
  (3, 'CPU', 'Google Tensor'),
  (3, 'RAM', '8GB'),
  (3, 'Storage', '128GB'),
  (3, 'Main Camera', '50MP + 12MP'),
  (3, 'Selfie Camera', '8MP'),
  (3, 'Battery', '4614 mAh'); 