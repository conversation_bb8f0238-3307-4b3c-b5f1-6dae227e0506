document.addEventListener('DOMContentLoaded', function() {
    // API URL
    const API_URL = 'http://localhost:3000/api';

    // Tab switching
    const tabs = document.querySelectorAll('.auth-tab');
    const tabContents = document.querySelectorAll('.auth-tab-content');

    // Check URL parameters to open specific tab
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');

    if (tabParam === 'register') {
        // Switch to register tab
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        const registerTab = document.querySelector('[data-tab="register-tab"]');
        const registerContent = document.getElementById('register-tab');

        if (registerTab && registerContent) {
            registerTab.classList.add('active');
            registerContent.classList.add('active');
        }
    }

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.getAttribute('data-tab');

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // Show corresponding content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === targetTab) {
                    content.classList.add('active');
                }
            });
        });
    });

    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.toggle-password');

    toggleButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordInput = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('register-password');
    const strengthMeter = document.querySelector('.strength-meter');
    const strengthText = document.querySelector('.strength-text');

    if (passwordInput) {
        passwordInput.addEventListener('input', updatePasswordStrength);
    }

    function updatePasswordStrength() {
        const password = passwordInput.value;
        let strength = 0;

        // Check password length
        if (password.length >= 8) strength += 25;

        // Check for uppercase letters
        if (/[A-Z]/.test(password)) strength += 25;

        // Check for lowercase letters
        if (/[a-z]/.test(password)) strength += 25;

        // Check for numbers or special characters
        if (/[0-9!@#$%^&*]/.test(password)) strength += 25;

        // Update strength meter
        strengthMeter.style.width = `${strength}%`;

        // Update color based on strength
        if (strength < 25) {
            strengthMeter.style.backgroundColor = '#ff4d4d';
            strengthText.textContent = 'Very Weak';
        } else if (strength < 50) {
            strengthMeter.style.backgroundColor = '#ffa64d';
            strengthText.textContent = 'Weak';
        } else if (strength < 75) {
            strengthMeter.style.backgroundColor = '#ffff4d';
            strengthText.textContent = 'Medium';
        } else {
            strengthMeter.style.backgroundColor = '#4CAF50';
            strengthText.textContent = 'Strong';
        }
    }

    // Form submissions
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const remember = document.getElementById('remember-me').checked;

            // Try client-side authentication for admin first
            if (email === '<EMAIL>' && password === 'Admin@123') {
                // Generate a simple token for admin
                const token = btoa(email + ':admin:' + Date.now());
                const userInfo = {
                    user_id: 1,
                    email: email,
                    first_name: 'Admin',
                    last_name: 'User',
                    role: 'admin'
                };

                // Use auth manager to login
                if (window.authManager) {
                    window.authManager.login(token, userInfo, remember);

                    // Execute any pending actions
                    window.authManager.executePendingAction();

                    // Redirect to admin dashboard
                    window.location.href = 'admin/index.html';
                } else {
                    // Fallback to old method
                    const storage = remember ? localStorage : sessionStorage;
                    storage.setItem('token', token);
                    storage.setItem('userRole', 'admin');
                    storage.setItem('userInfo', JSON.stringify(userInfo));
                    window.location.href = 'admin/index.html';
                }
                return;
            }

            // Try server authentication
            (async () => {
                try {
                    const response = await fetch(`${API_URL}/users/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });

                    const data = await response.json();
                    if (response.ok && data.success) {
                        // Use auth manager to login
                        if (window.authManager) {
                            window.authManager.login(data.token, data.user, remember);

                            // Execute any pending actions
                            window.authManager.executePendingAction();

                            // Redirect based on role
                            if (data.user.role === 'admin') {
                                window.location.href = 'admin/index.html';
                            } else {
                                window.location.href = 'index.html';
                            }
                        } else {
                            // Fallback to old method
                            const storage = remember ? localStorage : sessionStorage;
                            storage.setItem('token', data.token);
                            storage.setItem('userRole', data.user.role);
                            storage.setItem('userInfo', JSON.stringify(data.user));

                            if (data.user.role === 'admin') {
                                window.location.href = 'admin/index.html';
                            } else {
                                window.location.href = 'index.html';
                            }
                        }
                    } else {
                        throw new Error(data.message || 'Login failed');
                    }
                } catch (error) {
                    console.error('❌ Login API error:', error);
                    alert('Login failed. Please check your credentials and try again.');
                }
            })();
        });
    }

    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const firstName = document.getElementById('register-first-name').value;
            const lastName = document.getElementById('register-last-name').value;
            const email = document.getElementById('register-email').value;
            const phone = document.getElementById('register-phone').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;

            // Validate password match
            if (password !== confirmPassword) {
                alert('Passwords do not match.');
                return;
            }

            // Add user to data manager first (for admin visibility)
            if (window.addUser) {
                const userData = {
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    role: 'customer'
                };

                // Add to global data manager
                const newUser = window.addUser(userData);
                }

            // Try server authentication
            try {
                const response = await fetch(`${API_URL}/users/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email,
                        phone_number: phone,
                        password,
                        username: email.split('@')[0] // Generate username from email
                    })
                });

                const data = await response.json();
                if (response.ok && data.success) {
                    // Registration successful
                    alert('Registration successful! Please login with your new account.');

                    // Switch to login tab
                    document.querySelector('[data-tab="login-tab"]').click();

                    // Pre-fill login email
                    document.getElementById('login-email').value = email;
                    return;
                } else {
                    throw new Error(data.message || 'Registration failed');
                }
            } catch (error) {
                console.error('❌ Registration API error:', error);

                // Even if server fails, user is already added to data manager
                alert('Registration successful! Please login with your new account.');

                // Switch to login tab
                document.querySelector('[data-tab="login-tab"]').click();

                // Pre-fill login email
                document.getElementById('login-email').value = email;
            }
        });
    }
});