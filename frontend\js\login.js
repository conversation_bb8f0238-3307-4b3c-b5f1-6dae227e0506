document.addEventListener('DOMContentLoaded', function() {
    // API URL
    const API_URL = 'http://localhost:3000/api';

    // Check if user is already logged in and redirect accordingly
    checkExistingAuth();

    // Tab switching
    const tabs = document.querySelectorAll('.auth-tab');
    const tabContents = document.querySelectorAll('.auth-tab-content');

    // Check URL parameters to open specific tab
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');

    if (tabParam === 'register') {
        // Switch to register tab
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        const registerTab = document.querySelector('[data-tab="register-tab"]');
        const registerContent = document.getElementById('register-tab');

        if (registerTab && registerContent) {
            registerTab.classList.add('active');
            registerContent.classList.add('active');
        }
    }

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.getAttribute('data-tab');

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // Show corresponding content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === targetTab) {
                    content.classList.add('active');
                }
            });
        });
    });

    // Check for existing authentication and redirect accordingly
    function checkExistingAuth() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (token && user) {
            try {
                const userData = JSON.parse(user);
                redirectBasedOnRole(userData.role);
            } catch (error) {
                // Invalid user data, clear storage
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }
        }
    }

    function redirectBasedOnRole(role) {
        if (role === 'admin') {
            window.location.href = 'admin/admin-dashboard.html';
        } else {
            window.location.href = 'index.html';
        }
    }

    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.toggle-password');

    toggleButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordInput = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('register-password');
    const strengthMeter = document.querySelector('.strength-meter');
    const strengthText = document.querySelector('.strength-text');

    if (passwordInput) {
        passwordInput.addEventListener('input', updatePasswordStrength);
    }

    function updatePasswordStrength() {
        const password = passwordInput.value;
        let strength = 0;

        // Check password length
        if (password.length >= 8) strength += 25;

        // Check for uppercase letters
        if (/[A-Z]/.test(password)) strength += 25;

        // Check for lowercase letters
        if (/[a-z]/.test(password)) strength += 25;

        // Check for numbers or special characters
        if (/[0-9!@#$%^&*]/.test(password)) strength += 25;

        // Update strength meter
        strengthMeter.style.width = `${strength}%`;

        // Update color based on strength
        if (strength < 25) {
            strengthMeter.style.backgroundColor = '#ff4d4d';
            strengthText.textContent = 'Very Weak';
        } else if (strength < 50) {
            strengthMeter.style.backgroundColor = '#ffa64d';
            strengthText.textContent = 'Weak';
        } else if (strength < 75) {
            strengthMeter.style.backgroundColor = '#ffff4d';
            strengthText.textContent = 'Medium';
        } else {
            strengthMeter.style.backgroundColor = '#4CAF50';
            strengthText.textContent = 'Strong';
        }
    }

    // Form submissions
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const remember = document.getElementById('remember-me').checked;

            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Logging in...';
            submitBtn.disabled = true;

            try {
                // Call the real API for all authentication
                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Store authentication data
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    // Use auth manager if available
                    if (window.authManager) {
                        window.authManager.login(data.token, data.user, remember);
                        window.authManager.executePendingAction();
                    }

                    // Redirect based on user role
                    redirectBasedOnRole(data.user.role);
                } else {
                    throw new Error(data.message || 'Invalid email or password');
                }
            } catch (error) {
                console.error('Login error:', error);
                showLoginError(error.message || 'Login failed. Please check your credentials and try again.');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Show login error message
    function showLoginError(message) {
        // Check if error element already exists
        let errorElement = document.getElementById('login-error');

        if (!errorElement) {
            // Create error element
            errorElement = document.createElement('div');
            errorElement.id = 'login-error';
            errorElement.className = 'login-error';
            errorElement.style.cssText = `
                background-color: #f8d7da;
                color: #721c24;
                padding: 12px;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin-top: 15px;
                display: none;
            `;

            // Insert it after the login form
            if (loginForm) {
                loginForm.insertAdjacentElement('afterend', errorElement);
            }
        }

        // Update error message and show it
        errorElement.textContent = message;
        errorElement.style.display = 'block';

        // Hide error after 5 seconds
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const firstName = document.getElementById('register-first-name').value;
            const lastName = document.getElementById('register-last-name').value;
            const email = document.getElementById('register-email').value;
            const phone = document.getElementById('register-phone').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;

            // Validate password match
            if (password !== confirmPassword) {
                alert('Passwords do not match.');
                return;
            }

            // Add user to data manager first (for admin visibility)
            if (window.addUser) {
                const userData = {
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    role: 'customer'
                };

                // Add to global data manager
                const newUser = window.addUser(userData);
                }

            // Try server authentication
            try {
                const response = await fetch(`${API_URL}/users/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email,
                        phone_number: phone,
                        password,
                        username: email.split('@')[0] // Generate username from email
                    })
                });

                const data = await response.json();
                if (response.ok && data.success) {
                    // Registration successful
                    alert('Registration successful! Please login with your new account.');

                    // Switch to login tab
                    document.querySelector('[data-tab="login-tab"]').click();

                    // Pre-fill login email
                    document.getElementById('login-email').value = email;
                    return;
                } else {
                    throw new Error(data.message || 'Registration failed');
                }
            } catch (error) {
                console.error('❌ Registration API error:', error);

                // Even if server fails, user is already added to data manager
                alert('Registration successful! Please login with your new account.');

                // Switch to login tab
                document.querySelector('[data-tab="login-tab"]').click();

                // Pre-fill login email
                document.getElementById('login-email').value = email;
            }
        });
    }
});