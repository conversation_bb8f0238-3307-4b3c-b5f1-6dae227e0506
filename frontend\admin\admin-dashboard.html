<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Dashboard</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li class="active"><a href="index.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Dashboard</h1>
                <div class="admin-user">
                    <span>Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-box"></i></div>
                    <div class="stat-info">
                        <h3>Products</h3>
                        <p id="product-count">0</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-info">
                        <h3>Users</h3>
                        <p id="user-count">0</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
                    <div class="stat-info">
                        <h3>Orders</h3>
                        <p id="order-count">0</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-info">
                        <h3>Revenue</h3>
                        <p id="revenue">$0</p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-charts">
                <div class="chart-container">
                    <h2>Sales Overview</h2>
                    <canvas id="sales-chart"></canvas>
                </div>
                <div class="chart-container">
                    <h2>Top Categories</h2>
                    <canvas id="categories-chart"></canvas>
                </div>
            </div>
            
            <div class="recent-section">
                <h2>Recent Orders</h2>
                <div class="recent-orders-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="recent-orders">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <a href="orders.html" class="view-all">View All Orders</a>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/admin-app.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html> 