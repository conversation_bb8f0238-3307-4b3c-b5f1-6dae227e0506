<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - User Menu Fix</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .btn-test { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn-test:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <!-- Simulate header with auth buttons -->
    <div style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: bold; color: #007bff;">
                <i class="fas fa-mobile-alt"></i> PhoneHub
            </div>
            <div class="header-actions">
                <a href="#" class="cart-icon" style="margin-right: 15px;">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="#" class="login-btn">Login</a>
                    <a href="#" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </div>

    <h1>🧪 Test Rapide - Menu Utilisateur</h1>
    
    <div class="test-card">
        <h3>🔑 Étape 1 : Connexion</h3>
        <p>Connectez-vous pour voir le menu utilisateur :</p>
        <button class="btn-test btn-success" onclick="loginAsAdmin()">
            <i class="fas fa-user-shield"></i> Login Admin
        </button>
        <button class="btn-test" onclick="loginAsCustomer()">
            <i class="fas fa-user"></i> Login Client
        </button>
        <div id="login-status" class="status info" style="display: none;">
            ✅ Connecté ! Regardez l'en-tête - vous devriez voir "Hello, [Name]!" et un menu dropdown.
        </div>
    </div>
    
    <div class="test-card">
        <h3>👤 Étape 2 : Test Profile</h3>
        <p>Testez le bouton Profile :</p>
        <button class="btn-test" onclick="testProfile()" id="profile-btn" disabled>
            <i class="fas fa-user"></i> Test Profile Modal
        </button>
        <div id="profile-status" class="status success" style="display: none;">
            ✅ Modal Profile fonctionne ! Vous devriez voir vos informations utilisateur.
        </div>
    </div>
    
    <div class="test-card">
        <h3>📦 Étape 3 : Test My Orders</h3>
        <p>Testez le bouton My Orders :</p>
        <button class="btn-test" onclick="testOrders()" id="orders-btn" disabled>
            <i class="fas fa-shopping-bag"></i> Test Orders Modal
        </button>
        <div id="orders-status" class="status success" style="display: none;">
            ✅ Modal Orders fonctionne ! Vous devriez voir des commandes d'exemple.
        </div>
    </div>
    
    <div class="test-card">
        <h3>🚪 Étape 4 : Déconnexion</h3>
        <p>Testez la déconnexion :</p>
        <button class="btn-test btn-danger" onclick="logout()" id="logout-btn" disabled>
            <i class="fas fa-sign-out-alt"></i> Logout
        </button>
        <div id="logout-status" class="status info" style="display: none;">
            ✅ Déconnecté ! L'en-tête devrait maintenant montrer les boutons Login/Register.
        </div>
    </div>
    
    <div class="test-card">
        <h3>📋 Résumé du Test</h3>
        <ul id="test-results">
            <li>❓ Connexion utilisateur</li>
            <li>❓ Affichage menu utilisateur</li>
            <li>❓ Modal Profile fonctionnel</li>
            <li>❓ Modal My Orders fonctionnel</li>
            <li>❓ Déconnexion fonctionnelle</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="http://localhost:8080" class="btn-test">🏠 Retour au Site</a>
        <a href="http://localhost:8080/test-user-menu.html" class="btn-test">🧪 Test Complet</a>
    </div>

    <script src="frontend/js/auth.js"></script>
    <script src="frontend/js/app.js"></script>
    <script>
        function updateTestResults(step, success) {
            const results = document.getElementById('test-results');
            const items = results.children;
            if (items[step]) {
                items[step].textContent = items[step].textContent.replace('❓', success ? '✅' : '❌');
            }
        }

        function enableButtons() {
            document.getElementById('profile-btn').disabled = false;
            document.getElementById('orders-btn').disabled = false;
            document.getElementById('logout-btn').disabled = false;
        }

        function disableButtons() {
            document.getElementById('profile-btn').disabled = true;
            document.getElementById('orders-btn').disabled = true;
            document.getElementById('logout-btn').disabled = true;
        }

        function loginAsAdmin() {
            const token = btoa('<EMAIL>:admin:' + Date.now());
            const userInfo = {
                user_id: 1,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                created_at: '2023-01-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                document.getElementById('login-status').style.display = 'block';
                enableButtons();
                updateTestResults(0, true);
                updateTestResults(1, true);
            }
        }

        function loginAsCustomer() {
            const token = btoa('<EMAIL>:customer:' + Date.now());
            const userInfo = {
                user_id: 2,
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                role: 'customer',
                created_at: '2023-06-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                document.getElementById('login-status').style.display = 'block';
                enableButtons();
                updateTestResults(0, true);
                updateTestResults(1, true);
            }
        }

        function testProfile() {
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.showUserProfile();
                document.getElementById('profile-status').style.display = 'block';
                updateTestResults(2, true);
            }
        }

        function testOrders() {
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.showUserOrders();
                document.getElementById('orders-status').style.display = 'block';
                updateTestResults(3, true);
            }
        }

        function logout() {
            if (window.authManager) {
                window.authManager.logout();
                document.getElementById('logout-status').style.display = 'block';
                disableButtons();
                updateTestResults(4, true);
                
                // Hide other status messages
                document.getElementById('login-status').style.display = 'none';
                document.getElementById('profile-status').style.display = 'none';
                document.getElementById('orders-status').style.display = 'none';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Quick test page loaded');
        });
    </script>
</body>
</html>
