// Admin login script with real API authentication
document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    const token = localStorage.getItem('token');
    if (token && window.location.pathname.includes('login.html')) {
        // Verify token is still valid
        verifyTokenAndRedirect();
    }

    // Handle login form submission
    const loginForm = document.getElementById('admin-login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;

            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Logging in...';
            submitBtn.disabled = true;

            try {
                // Call the real API
                const response = await fetch('http://localhost:3000/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.success && data.user.role === 'admin') {
                    // Store authentication data
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    // Redirect to dashboard
                    window.location.href = 'index.html';
                } else if (data.success && data.user.role !== 'admin') {
                    showLoginError('Access denied. Admin privileges required.');
                } else {
                    showLoginError(data.message || 'Invalid email or password. Please try again.');
                }
            } catch (error) {
                console.error('Login error:', error);
                showLoginError('Connection error. Please check your internet connection and try again.');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Verify token and redirect if valid
    async function verifyTokenAndRedirect() {
        try {
            const response = await fetch('http://localhost:3000/api/admin/dashboard', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                window.location.href = 'index.html';
            } else {
                // Token is invalid, remove it
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }
        } catch (error) {
            // Network error, stay on login page
        }
    }

    // Show login error message
    function showLoginError(message) {
        // Check if error element already exists
        let errorElement = document.getElementById('login-error');

        if (!errorElement) {
            // Create error element
            errorElement = document.createElement('div');
            errorElement.id = 'login-error';
            errorElement.className = 'login-error';
            errorElement.style.cssText = `
                background-color: #f8d7da;
                color: #721c24;
                padding: 12px;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin-top: 15px;
                display: none;
            `;

            // Insert it after the form
            const loginForm = document.getElementById('admin-login-form');
            if (loginForm) {
                loginForm.insertAdjacentElement('afterend', errorElement);
            }
        }

        // Update error message and show it
        errorElement.textContent = message;
        errorElement.style.display = 'block';

        // Add shake animation to login box
        const loginBox = document.querySelector('.login-box');
        if (loginBox) {
            loginBox.classList.add('error');
            setTimeout(() => loginBox.classList.remove('error'), 500);
        }

        // Hide error after 5 seconds
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    // Handle logout button (on other pages)
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Clear authentication data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('admin_token'); // Legacy cleanup
            localStorage.removeItem('admin_user'); // Legacy cleanup

            // Redirect to login page
            window.location.href = 'login.html';
        });
    }
});