// Simple admin login script
document.addEventListener('DOMContentLoaded', function() {
    // Handle login form submission
    const loginForm = document.getElementById('admin-login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;

            // Hardcoded admin credentials
            if (email === '<EMAIL>' && password === 'Admin@123') {
                // Store authentication data
                localStorage.setItem('admin_token', 'admin-token-' + Date.now());
                localStorage.setItem('admin_user', JSON.stringify({
                    email: email,
                    name: 'Admin User',
                    role: 'admin'
                }));

                // Redirect to dashboard
                window.location.href = 'index.html';
            } else {
                // Show error message
                showLoginError('Invalid email or password. Please try again.');
            }
        });
    } else {
        }

    // Show login error message
    function showLoginError(message) {
        // Check if error element already exists
        let errorElement = document.getElementById('login-error');

        if (!errorElement) {
            // Create error element
            errorElement = document.createElement('div');
            errorElement.id = 'login-error';
            errorElement.className = 'login-error';

            // Insert it after the form
            loginForm.insertAdjacentElement('afterend', errorElement);
        }

        // Update error message and show it
        errorElement.textContent = message;
        errorElement.style.display = 'block';

        // Add shake animation to login box
        document.querySelector('.login-box').classList.add('error');
    }

    // Handle logout button (on other pages)
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Clear authentication data
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');

            // Redirect to login page
            window.location.href = 'login.html';
        });
    }
});