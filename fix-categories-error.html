<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Categories Error - RaoufStore</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .fix-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .console-output { background: #000; color: #00ff00; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Fix Categories Error - RaoufStore</h1>
    
    <div class="fix-card">
        <h2>❌ Problème Identifié</h2>
        <div class="status error">
            <strong>Erreur :</strong> "Failed to load categories. Please refresh the page."
        </div>
        
        <p><strong>Cause :</strong> Les catégories et marques ne sont pas initialisées dans le data manager.</p>
        <p><strong>Solution :</strong> Réinitialiser les données avec les catégories et marques.</p>
    </div>
    
    <div class="fix-card">
        <h2>🔧 Actions de Correction</h2>
        
        <button class="btn danger" onclick="clearAllData()">
            🗑️ Vider toutes les données
        </button>
        
        <button class="btn success" onclick="initializeData()">
            ✅ Réinitialiser les données
        </button>
        
        <button class="btn" onclick="checkDataStatus()">
            🔍 Vérifier le statut
        </button>
        
        <button class="btn" onclick="goToProducts()">
            📱 Aller aux produits
        </button>
    </div>
    
    <div class="fix-card">
        <h2>📊 Statut des Données</h2>
        <div id="data-status" class="status info">
            Cliquez sur "Vérifier le statut" pour voir les données actuelles.
        </div>
    </div>
    
    <div class="fix-card">
        <h2>🖥️ Console Output</h2>
        <div id="console-output" class="console-output">
            Console logs apparaîtront ici...
        </div>
    </div>
    
    <div class="fix-card">
        <h2>📋 Instructions</h2>
        <ol>
            <li><strong>Vider les données</strong> - Supprime toutes les données stockées</li>
            <li><strong>Réinitialiser</strong> - Recrée les données avec catégories et marques</li>
            <li><strong>Vérifier</strong> - Confirme que tout fonctionne</li>
            <li><strong>Tester</strong> - Aller à la page produits pour vérifier</li>
        </ol>
    </div>

    <script src="frontend/js/data-manager.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }
        
        function clearAllData() {
            log('🗑️ Clearing all localStorage data...');
            
            // Clear all RaoufStore data
            localStorage.removeItem('raoufstore_users');
            localStorage.removeItem('raoufstore_orders');
            localStorage.removeItem('raoufstore_products');
            localStorage.removeItem('raoufstore_categories');
            localStorage.removeItem('raoufstore_brands');
            
            // Clear old PhoneHub data if exists
            localStorage.removeItem('phonehub_users');
            localStorage.removeItem('phonehub_orders');
            localStorage.removeItem('phonehub_products');
            
            log('✅ All data cleared from localStorage');
            
            // Reset global data
            if (window.RaoufStoreData) {
                window.RaoufStoreData.users = [];
                window.RaoufStoreData.orders = [];
                window.RaoufStoreData.products = [];
                window.RaoufStoreData.categories = [];
                window.RaoufStoreData.brands = [];
                window.RaoufStoreData.initialized = false;
                log('✅ Global data reset');
            }
            
            checkDataStatus();
        }
        
        function initializeData() {
            log('🔄 Initializing RaoufStore data...');
            
            try {
                // Force re-initialization
                if (window.initializeDataManager) {
                    window.initializeDataManager();
                    log('✅ Data manager initialized');
                } else {
                    log('❌ Data manager function not found');
                }
                
                // Check if data was created
                setTimeout(() => {
                    checkDataStatus();
                }, 1000);
                
            } catch (error) {
                log('❌ Error initializing data: ' + error.message);
            }
        }
        
        function checkDataStatus() {
            log('🔍 Checking data status...');
            
            if (window.RaoufStoreData) {
                log('📊 RaoufStoreData exists');
                log(`👥 Users: ${window.RaoufStoreData.users.length}`);
                log(`🛒 Orders: ${window.RaoufStoreData.orders.length}`);
                log(`📱 Products: ${window.RaoufStoreData.products.length}`);
                log(`📂 Categories: ${window.RaoufStoreData.categories.length}`);
                log(`🏷️ Brands: ${window.RaoufStoreData.brands.length}`);
                log(`✅ Initialized: ${window.RaoufStoreData.initialized}`);
                
                // Update status display
                const statusDiv = document.getElementById('data-status');
                if (window.RaoufStoreData.categories.length > 0 && window.RaoufStoreData.brands.length > 0) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        <strong>✅ Données OK !</strong><br>
                        Categories: ${window.RaoufStoreData.categories.length} | 
                        Brands: ${window.RaoufStoreData.brands.length} | 
                        Products: ${window.RaoufStoreData.products.length}
                    `;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `
                        <strong>❌ Données manquantes !</strong><br>
                        Categories: ${window.RaoufStoreData.categories.length} | 
                        Brands: ${window.RaoufStoreData.brands.length}
                    `;
                }
            } else {
                log('❌ RaoufStoreData not found');
                document.getElementById('data-status').className = 'status error';
                document.getElementById('data-status').innerHTML = '<strong>❌ RaoufStoreData not found</strong>';
            }
        }
        
        function goToProducts() {
            log('📱 Redirecting to products page...');
            window.location.href = 'http://localhost:8080/frontend/products.html';
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Fix Categories Error page loaded');
            setTimeout(checkDataStatus, 1000);
        });
    </script>
</body>
</html>
