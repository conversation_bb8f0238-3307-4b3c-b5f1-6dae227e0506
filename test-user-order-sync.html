<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User & Order Sync - PhoneHub</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .test-button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.warning { background: #ffc107; color: #212529; }
        .test-button.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .fix-item { background: white; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
        .problem-item { background: white; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
        .code-block { background: #f8f9fa; border: 1px solid #e1e5eb; border-radius: 4px; padding: 15px; margin: 10px 0; font-family: 'Courier New', monospace; font-size: 14px; }
        .step-list { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; }
        .step-item { display: flex; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .step-item:last-child { border-bottom: none; }
        .step-number { background: #007bff; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold; }
        .step-content { flex: 1; }
        .step-title { font-weight: bold; margin-bottom: 5px; }
        .step-desc { color: #666; font-size: 0.9rem; }
    </style>
</head>
<body>
    <!-- Header with auth -->
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>PhoneHub</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="frontend/login.html" class="login-btn">Login</a>
                    <a href="frontend/login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
        <h1>🔄 Test Synchronisation Utilisateurs & Commandes</h1>
        
        <div class="test-card">
            <h2>🎯 Objectif du Test</h2>
            
            <div class="status info">
                <h4>Problèmes à Résoudre :</h4>
                <ul>
                    <li>✅ <strong>Nouveaux utilisateurs</strong> n'apparaissent pas dans Admin → Users</li>
                    <li>✅ <strong>Nouvelles commandes</strong> n'apparaissent pas dans Admin → Orders</li>
                    <li>✅ <strong>Synchronisation</strong> entre frontend et admin</li>
                    <li>✅ <strong>Persistance des données</strong> avec localStorage</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🔧 Solutions Implémentées</h2>
            
            <div class="fix-item">
                <h4>1. Data Manager Global</h4>
                <div class="code-block">
// Gestionnaire de données centralisé
window.PhoneHubData = {
    users: [],
    orders: [],
    initialized: false
};

// Fonctions globales
- addUser(userData) → Ajoute un utilisateur
- addOrder(orderData) → Ajoute une commande
- getAllUsers() → Récupère tous les utilisateurs
- getAllOrders() → Récupère toutes les commandes
- saveDataToStorage() → Sauvegarde dans localStorage
                </div>
            </div>
            
            <div class="fix-item">
                <h4>2. Inscription Synchronisée</h4>
                <div class="code-block">
// Dans login.js - lors de l'inscription
if (window.addUser) {
    const userData = {
        first_name: firstName,
        last_name: lastName,
        email: email,
        phone: phone,
        role: 'customer'
    };
    
    const newUser = window.addUser(userData);
    console.log('👤 User added to data manager:', newUser);
}
                </div>
            </div>
            
            <div class="fix-item">
                <h4>3. Commandes Synchronisées</h4>
                <div class="code-block">
// Dans cart.js - lors du checkout
if (window.addOrder) {
    const orderData = {
        user_id: user.user_id,
        customer_name: `${user.first_name} ${user.last_name}`,
        customer_email: user.email,
        total: total,
        status: 'pending',
        payment_status: 'paid',
        shipping_address: user.address,
        items: cart.map(item => ({
            product_name: item.name,
            quantity: item.quantity,
            price: item.price,
            subtotal: item.price * item.quantity
        }))
    };
    
    const newOrder = window.addOrder(orderData);
}
                </div>
            </div>
            
            <div class="fix-item">
                <h4>4. Pages Admin Mises à Jour</h4>
                <div class="code-block">
// users.js - charge depuis data manager
function loadUsers() {
    if (window.PhoneHubData && window.PhoneHubData.users) {
        users = [...window.PhoneHubData.users];
    } else if (window.getAllUsers) {
        users = window.getAllUsers();
    }
}

// orders.js - charge depuis data manager
function loadOrders() {
    if (window.PhoneHubData && window.PhoneHubData.orders) {
        allOrders = [...window.PhoneHubData.orders];
    } else if (window.getAllOrders) {
        allOrders = window.getAllOrders();
    }
}
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🧪 Procédure de Test</h2>
            
            <div class="step-list">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Inscription Nouvel Utilisateur</div>
                        <div class="step-desc">Créer un compte avec un nouvel email pour tester l'ajout d'utilisateur</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Vérification Admin Users</div>
                        <div class="step-desc">Vérifier que le nouvel utilisateur apparaît dans Admin → Users</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Passer une Commande</div>
                        <div class="step-desc">Ajouter des produits au panier et finaliser une commande</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">Vérification Admin Orders</div>
                        <div class="step-desc">Vérifier que la nouvelle commande apparaît dans Admin → Orders</div>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-title">Test de Persistance</div>
                        <div class="step-desc">Rafraîchir les pages pour vérifier que les données persistent</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🚀 Liens de Test</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <a href="http://localhost:8080/frontend/login.html?tab=register" class="test-button success">
                    👤 1. Inscription Utilisateur
                </a>
                <a href="http://localhost:8080/frontend/admin/users.html" class="test-button warning">
                    👥 2. Admin Users
                </a>
                <a href="http://localhost:8080/frontend/products.html" class="test-button">
                    🛒 3. Ajouter au Panier
                </a>
                <a href="http://localhost:8080/frontend/cart.html" class="test-button">
                    💳 4. Finaliser Commande
                </a>
                <a href="http://localhost:8080/frontend/admin/orders.html" class="test-button warning">
                    📦 5. Admin Orders
                </a>
                <a href="http://localhost:8080/frontend/admin/index.html" class="test-button danger">
                    🔧 6. Admin Dashboard
                </a>
            </div>
        </div>
        
        <div class="test-card">
            <h2>📊 Données de Test</h2>
            
            <div class="status info">
                <h4>Compte Admin :</h4>
                <ul>
                    <li><strong>Email :</strong> <EMAIL></li>
                    <li><strong>Mot de passe :</strong> Admin@123</li>
                </ul>
                
                <h4>Nouvel Utilisateur (à créer) :</h4>
                <ul>
                    <li><strong>Prénom :</strong> Test</li>
                    <li><strong>Nom :</strong> User</li>
                    <li><strong>Email :</strong> <EMAIL></li>
                    <li><strong>Téléphone :</strong> ************</li>
                    <li><strong>Mot de passe :</strong> Test@123</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>✅ Vérifications Attendues</h2>
            
            <div class="status success">
                <h4>Après Inscription :</h4>
                <ul>
                    <li>✅ Utilisateur visible dans Admin → Users</li>
                    <li>✅ Données complètes (nom, email, téléphone, rôle)</li>
                    <li>✅ Date d'inscription correcte</li>
                </ul>
                
                <h4>Après Commande :</h4>
                <ul>
                    <li>✅ Commande visible dans Admin → Orders</li>
                    <li>✅ Informations client correctes</li>
                    <li>✅ Produits et prix corrects</li>
                    <li>✅ Statut "pending" ou "processing"</li>
                </ul>
                
                <h4>Persistance :</h4>
                <ul>
                    <li>✅ Données conservées après rafraîchissement</li>
                    <li>✅ Synchronisation entre toutes les pages</li>
                </ul>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🔍 Debug Console</h2>
            
            <div class="status warning">
                <h4>Messages Console Attendus :</h4>
                <div class="code-block">
📊 Loading Data Manager...
📊 Initializing Data Manager...
👥 Sample users initialized
🛒 Sample orders initialized
✅ Data Manager initialized
👥 Users: 3
🛒 Orders: 3

// Lors de l'inscription
👤 User added to data manager: {id: 4, email: "<EMAIL>", ...}
💾 Data saved to storage

// Lors d'une commande
🛒 Order created: {id: 1004, customer_email: "<EMAIL>", ...}
📊 Updated user stats for: <EMAIL>
💾 Data saved to storage

// Dans les pages admin
👥 Loading users from data manager...
👥 Loaded users from data manager: 4
🛒 Loading orders from data manager...
🛒 Loaded orders from data manager: 4
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 12px;">
            <h2>🔄 Synchronisation Utilisateurs & Commandes</h2>
            <p style="font-size: 18px; margin: 20px 0;">
                Data Manager ✅ | Inscription Sync ✅ | Commandes Sync ✅ | Admin Pages ✅
            </p>
            <a href="http://localhost:8080/frontend/login.html?tab=register" class="test-button success" style="font-size: 16px; padding: 12px 24px;">
                🚀 Commencer le Test
            </a>
        </div>
    </div>

    <script src="frontend/js/data-manager.js"></script>
    <script src="frontend/js/auth.js"></script>
    <script>
        // Debug logging
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] [USER ORDER SYNC] ${message}`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('User & Order sync test page loaded');
            debugLog('Data Manager status: ' + (window.PhoneHubData ? 'Loaded' : 'Not loaded'));
            
            if (window.PhoneHubData) {
                debugLog('Users in data manager: ' + window.PhoneHubData.users.length);
                debugLog('Orders in data manager: ' + window.PhoneHubData.orders.length);
            }
        });
    </script>
</body>
</html>
