// Shopping Cart Page JavaScript
document.addEventListener('DOMContentLoaded', () => {
    // Check authentication first
    if (!window.authManager || !window.authManager.isAuthenticated()) {
        // Show authentication required message
        document.body.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; min-height: 100vh; flex-direction: column; text-align: center; padding: 20px;">
                <div style="max-width: 400px;">
                    <i class="fas fa-lock" style="font-size: 48px; color: #6c757d; margin-bottom: 20px;"></i>
                    <h2 style="color: #343a40; margin-bottom: 15px;">Authentication Required</h2>
                    <p style="color: #6c757d; margin-bottom: 30px;">You need to login or register to view your shopping cart.</p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <a href="login.html" class="btn primary-btn" style="padding: 12px 24px; text-decoration: none;">Login</a>
                        <a href="login.html?tab=register" class="btn secondary-btn" style="padding: 12px 24px; text-decoration: none;">Register</a>
                    </div>
                    <p style="margin-top: 20px;"><a href="index.html" style="color: #007bff;">← Back to Home</a></p>
                </div>
            </div>
        `;
        return;
    }

    // DOM Elements
    const cartItemsContainer = document.getElementById('cart-items-container');
    const cartItemsTbody = document.getElementById('cart-items-tbody');
    const emptyCartMessage = document.getElementById('empty-cart-message');
    const summaryItemsCount = document.getElementById('summary-items-count');
    const summarySubtotal = document.getElementById('summary-subtotal');
    const summaryDiscount = document.getElementById('summary-discount');
    const discountRow = document.getElementById('discount-row');
    const summaryShipping = document.getElementById('summary-shipping');
    const summaryTotal = document.getElementById('summary-total');
    const checkoutBtn = document.getElementById('checkout-btn');
    const clearCartBtn = document.getElementById('clear-cart');
    const applyCouponBtn = document.getElementById('apply-coupon');
    const couponCodeInput = document.getElementById('coupon-code');
    const recentlyViewedProducts = document.getElementById('recently-viewed-products');

    // State
    let cart = [];
    let discount = 0;
    let shippingCost = 0;
    let hasAppliedCoupon = false;

    // Initialize
    init();

    // Initialize the page
    async function init() {
        // Load cart from API or localStorage
        await loadCart();

        // Render cart
        renderCart();

        // Load recently viewed products
        loadRecentlyViewedProducts();

        // Add event listeners
        if (clearCartBtn) {
            clearCartBtn.addEventListener('click', clearCart);
        }

        if (applyCouponBtn) {
            applyCouponBtn.addEventListener('click', applyCoupon);
        }

        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', proceedToCheckout);
        }
    }

    // Load cart from API or localStorage fallback
    async function loadCart() {
        try {
            console.log('🔄 Loading cart from API...');
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');

            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${window.API_URL}/carts`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('📦 Cart API response:', data);

                if (data.success && data.cart) {
                    // Convert API format to frontend format
                    cart = data.cart.items.map(item => ({
                        id: item.product_id,
                        name: item.name,
                        price: parseFloat(item.price),
                        image: item.primary_image || 'images/real-products/iphone13_main.jpg',
                        quantity: item.quantity,
                        cart_item_id: item.cart_item_id
                    }));
                    console.log('✅ Cart loaded from API:', cart.length, 'items');
                    return;
                }
            }

            throw new Error('API request failed');
        } catch (error) {
            console.log('⚠️ Falling back to localStorage for cart');

            // Fallback to localStorage
            const userId = window.authManager?.getCurrentUser()?.user_id || 'guest';
            const cartKey = `cart_${userId}`;
            const savedCart = localStorage.getItem(cartKey);
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                } catch (e) {
                    console.error('Error parsing cart from localStorage:', e);
                    cart = [];
                }
            }
        }
    }

    // Save cart to localStorage (user-specific)
    function saveCart() {
        const userId = window.authManager?.getCurrentUser()?.user_id || 'guest';
        const cartKey = `cart_${userId}`;
        localStorage.setItem(cartKey, JSON.stringify(cart));

        // Update cart count in the header
        updateCartCount();
    }

    // Render cart
    function renderCart() {
        if (cart.length === 0) {
            // Show empty cart message
            emptyCartMessage.style.display = 'block';
            cartItemsContainer.style.display = 'none';
            checkoutBtn.disabled = true;
            
            // Update summary
            updateSummary(0, 0, 0, 0);
        } else {
            // Hide empty cart message and show cart items
            emptyCartMessage.style.display = 'none';
            cartItemsContainer.style.display = 'block';
            checkoutBtn.disabled = false;
            
            // Render cart items
            renderCartItems();
            
            // Calculate totals
            const subtotal = calculateSubtotal();
            
            // Calculate shipping (free for orders over 6700 DZD, otherwise 800 DZD)
            shippingCost = subtotal > 6700 ? 0 : 800;
            
            // Apply discount if there's a coupon
            const total = subtotal - discount + shippingCost;
            
            // Update summary
            updateSummary(cart.length, subtotal, discount, total);
        }
    }

    // Render cart items
    function renderCartItems() {
        cartItemsTbody.innerHTML = '';
        
        cart.forEach((item, index) => {
            const itemTotal = item.price * item.quantity;
            
            const row = document.createElement('tr');
            row.dataset.id = item.id;
            
            row.innerHTML = `
                <td>
                    <div class="product-info-cart">
                        <div class="product-image-cart">
                            <img src="${item.image}" alt="${item.name}">
                        </div>
                        <div class="product-name-cart">${item.name}</div>
                    </div>
                </td>
                <td class="product-price-cart">${item.price.toLocaleString()} DZD</td>
                <td>
                    <div class="quantity-selector-cart">
                        <button class="quantity-btn-cart decrease-btn" data-index="${index}">-</button>
                        <input type="number" class="cart-quantity-input" value="${item.quantity}" min="1" data-index="${index}">
                        <button class="quantity-btn-cart increase-btn" data-index="${index}">+</button>
                    </div>
                </td>
                <td class="product-total-cart">${itemTotal.toLocaleString()} DZD</td>
                <td>
                    <button class="remove-item-btn" data-index="${index}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            
            cartItemsTbody.appendChild(row);
        });
        
        // Add event listeners to buttons
        const decreaseBtns = document.querySelectorAll('.decrease-btn');
        const increaseBtns = document.querySelectorAll('.increase-btn');
        const quantityInputs = document.querySelectorAll('.cart-quantity-input');
        const removeItemBtns = document.querySelectorAll('.remove-item-btn');
        
        decreaseBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                decreaseQuantity(index);
            });
        });
        
        increaseBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                increaseQuantity(index);
            });
        });
        
        quantityInputs.forEach(input => {
            input.addEventListener('change', () => {
                const index = parseInt(input.dataset.index);
                updateItemQuantity(index, parseInt(input.value));
            });
        });
        
        removeItemBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                removeItem(index);
            });
        });
    }

    // Calculate subtotal
    function calculateSubtotal() {
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Update order summary
    function updateSummary(itemsCount, subtotal, discount, total) {
        summaryItemsCount.textContent = itemsCount;
        summarySubtotal.textContent = `${subtotal.toLocaleString()} DZD`;

        if (discount > 0) {
            discountRow.style.display = 'flex';
            summaryDiscount.textContent = `-${discount.toLocaleString()} DZD`;
        } else {
            discountRow.style.display = 'none';
        }

        summaryShipping.textContent = shippingCost === 0 ? 'Free' : `${shippingCost.toLocaleString()} DZD`;
        summaryTotal.textContent = `${total.toLocaleString()} DZD`;
    }

    // Decrease item quantity
    function decreaseQuantity(index) {
        if (cart[index].quantity > 1) {
            cart[index].quantity--;
            saveCart();
            renderCart();
        }
    }

    // Increase item quantity
    function increaseQuantity(index) {
        cart[index].quantity++;
        saveCart();
        renderCart();
    }

    // Update item quantity
    function updateItemQuantity(index, newQuantity) {
        if (newQuantity < 1) {
            newQuantity = 1;
        }
        
        cart[index].quantity = newQuantity;
        saveCart();
        renderCart();
    }

    // Remove item from cart
    async function removeItem(index) {
        if (confirm('Are you sure you want to remove this item from your cart?')) {
            const item = cart[index];

            try {
                if (item.cart_item_id) {
                    console.log('🗑️ Removing item from cart via API...');
                    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                    const response = await fetch(`${window.API_URL}/carts/items/${item.cart_item_id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        console.log('✅ Item removed via API');
                        await loadCart(); // Reload cart from API
                        renderCart();
                        return;
                    }
                }

                throw new Error('API removal failed');
            } catch (error) {
                console.log('⚠️ Falling back to localStorage removal');
                cart.splice(index, 1);
                saveCart();
                renderCart();
            }
        }
    }

    // Clear cart
    async function clearCart() {
        if (confirm('Are you sure you want to clear your entire cart?')) {
            try {
                console.log('🗑️ Clearing cart via API...');
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                const response = await fetch(`${window.API_URL}/carts`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    console.log('✅ Cart cleared via API');
                    cart = [];
                    renderCart();
                    return;
                }

                throw new Error('API clear failed');
            } catch (error) {
                console.log('⚠️ Falling back to localStorage clear');
                cart = [];
                saveCart();
                renderCart();
            }
        }
    }

    // Apply coupon
    function applyCoupon() {
        const couponCode = couponCodeInput.value.trim().toUpperCase();
        
        if (!couponCode) {
            alert('Please enter a coupon code');
            return;
        }
        
        if (hasAppliedCoupon) {
            alert('You have already applied a coupon');
            return;
        }
        
        // For demo purposes, we'll just use a few fixed coupon codes
        const validCoupons = {
            'WELCOME10': 10,
            'SUMMER15': 15,
            'SAVE20': 20
        };
        
        if (validCoupons[couponCode]) {
            const discountPercentage = validCoupons[couponCode];
            const subtotal = calculateSubtotal();
            discount = subtotal * (discountPercentage / 100);
            
            hasAppliedCoupon = true;
            renderCart();
            
            showNotification(`Coupon applied! ${discountPercentage}% off your order.`);
            
            // Disable the coupon input and button
            couponCodeInput.disabled = true;
            applyCouponBtn.disabled = true;
        } else {
            alert('Invalid coupon code');
        }
    }

    // Proceed to checkout
    function proceedToCheckout() {
        // Double-check authentication
        if (!window.authManager || !window.authManager.isAuthenticated()) {
            window.authManager?.showAuthModal('You need to be logged in to proceed to checkout.');
            return;
        }

        // Check if cart is not empty
        if (cart.length === 0) {
            alert('Your cart is empty. Add some products before checkout.');
            return;
        }

        // Get user info
        const user = window.authManager.getCurrentUser();
        const subtotal = calculateSubtotal();
        const total = subtotal - discount + shippingCost;

        // Create order summary
        const orderSummary = {
            user_id: user.user_id,
            user_email: user.email,
            items: cart,
            subtotal: subtotal,
            discount: discount,
            shipping: shippingCost,
            total: total,
            timestamp: new Date().toISOString()
        };

        // For demo purposes, show order confirmation
        const confirmMessage = `
Order Summary:
Customer: ${user.first_name} ${user.last_name}
Email: ${user.email}
Items: ${cart.length} product(s)
Subtotal: ${subtotal.toLocaleString()} DZD
${discount > 0 ? `Discount: -${discount.toLocaleString()} DZD\n` : ''}Shipping: ${shippingCost === 0 ? 'Free' : shippingCost.toLocaleString() + ' DZD'}
Total: ${total.toLocaleString()} DZD

Proceed with this order?`;

        if (confirm(confirmMessage)) {
            // Create order in data manager
            if (window.addOrder) {
                const orderData = {
                    user_id: user.user_id,
                    customer_name: `${user.first_name} ${user.last_name}`,
                    customer_email: user.email,
                    total: total,
                    status: 'pending',
                    payment_status: 'paid',
                    shipping_address: user.address || 'Address not provided',
                    items: cart.map(item => ({
                        product_name: item.name,
                        quantity: item.quantity,
                        price: item.price,
                        subtotal: item.price * item.quantity
                    }))
                };

                const newOrder = window.addOrder(orderData);
                console.log('🛒 Order created:', newOrder);
            }

            // Save order to user's order history
            const userId = user.user_id;
            const orderHistoryKey = `orders_${userId}`;
            let userOrders = [];

            const savedOrders = localStorage.getItem(orderHistoryKey);
            if (savedOrders) {
                try {
                    userOrders = JSON.parse(savedOrders);
                } catch (e) {
                    console.error('Error parsing user orders:', e);
                }
            }

            // Add new order to user's history
            const newOrder = {
                id: `ORD${Date.now()}`,
                date: new Date().toISOString(),
                items: cart,
                total: total,
                status: 'processing'
            };

            userOrders.unshift(newOrder);
            localStorage.setItem(orderHistoryKey, JSON.stringify(userOrders));

            // Simulate order processing
            showNotification('Order placed successfully! Thank you for your purchase.');

            // Clear cart after successful order
            cart = [];
            saveCart();
            renderCart();

            // In a real application, this would:
            // 1. Send order to backend API
            // 2. Process payment
            // 3. Send confirmation email
            // 4. Redirect to order confirmation page
        }
    }

    // Load recently viewed products
    function loadRecentlyViewedProducts() {
        // Get recently viewed products from localStorage
        const viewedProducts = localStorage.getItem('recentlyViewed');
        
        if (viewedProducts) {
            try {
                const products = JSON.parse(viewedProducts);
                
                if (products.length > 0) {
                    renderRecentlyViewedProducts(products);
                    return;
                }
            } catch (e) {
                console.error('Error parsing recently viewed products:', e);
            }
        }
        
        // Fallback to sample products
        generateSampleRecentlyViewed();
    }

    // Generate sample recently viewed products
    function generateSampleRecentlyViewed() {
        const sampleProducts = [
            {
                product_id: 1,
                name: 'iPhone 13 Pro Max',
                price: 1099.99,
                images: [{ image_url: 'images/placeholder.svg' }]
            },
            {
                product_id: 2,
                name: 'Samsung Galaxy S21',
                price: 799.99,
                images: [{ image_url: 'images/placeholder.svg' }]
            },
            {
                product_id: 3,
                name: 'Google Pixel 6',
                price: 699.00,
                images: [{ image_url: 'images/placeholder.svg' }]
            },
            {
                product_id: 4,
                name: 'OnePlus 9 Pro',
                price: 969.00,
                images: [{ image_url: 'images/placeholder.svg' }]
            }
        ];
        
        renderRecentlyViewedProducts(sampleProducts);
    }

    // Render recently viewed products
    function renderRecentlyViewedProducts(products) {
        recentlyViewedProducts.innerHTML = '';
        
        products.forEach(product => {
            const productCard = createProductCard(product);
            recentlyViewedProducts.appendChild(productCard);
        });
        
        // Add event listeners to the new "Add to Cart" buttons
        const addToCartButtons = recentlyViewedProducts.querySelectorAll('.add-to-cart-btn');
        addToCartButtons.forEach(button => {
            button.addEventListener('click', handleAddToCart);
        });
    }
}); 