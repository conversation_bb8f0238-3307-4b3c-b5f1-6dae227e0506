<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Cart - PhoneHub</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>PhoneHub</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
            </div>
        </div>
    </header>

    <section style="padding: 50px 0;">
        <div class="container">
            <h1>Debug Cart Test</h1>
            <p>Cliquez sur le bouton ci-dessous pour tester l'ajout au panier :</p>
            
            <div class="product-card" style="max-width: 300px; margin: 20px 0;">
                <div class="product-image">
                    <img src="frontend/images/real-products/iphone13_main.jpg" alt="Test Product">
                </div>
                <div class="product-info">
                    <h3>Test Product</h3>
                    <div class="product-price">$99.99</div>
                </div>
                <div class="product-actions">
                    <button class="btn primary-btn add-to-cart-btn" 
                            data-id="999" 
                            data-name="Test Product" 
                            data-price="99.99" 
                            data-image="frontend/images/real-products/iphone13_main.jpg">
                        <i class="fas fa-cart-plus"></i> Add to Cart
                    </button>
                </div>
            </div>
            
            <div id="debug-log" style="margin-top: 30px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
                <h3>Debug Log:</h3>
                <div id="log-content"></div>
            </div>
        </div>
    </section>

    <script>
        // Debug logging
        function debugLog(message) {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(`[DEBUG] ${message}`);
        }

        // Override the original functions to add logging
        const originalHandleAddToCart = window.handleAddToCart;
        
        // Count how many times the function is called
        let callCount = 0;
        
        window.handleAddToCart = function(event) {
            callCount++;
            debugLog(`handleAddToCart called (call #${callCount})`);
            
            if (originalHandleAddToCart) {
                return originalHandleAddToCart(event);
            } else {
                debugLog('Original handleAddToCart not found, using fallback');
                // Fallback implementation
                const button = event.target.closest('.add-to-cart-btn');
                if (!button) return;
                
                debugLog(`Button found: ${button.dataset.name}`);
                
                // Simple cart update
                let cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItem = cart.find(item => item.id === button.dataset.id);
                
                if (existingItem) {
                    existingItem.quantity += 1;
                    debugLog(`Updated existing item quantity to ${existingItem.quantity}`);
                } else {
                    cart.push({
                        id: button.dataset.id,
                        name: button.dataset.name,
                        price: parseFloat(button.dataset.price),
                        image: button.dataset.image,
                        quantity: 1
                    });
                    debugLog('Added new item to cart');
                }
                
                localStorage.setItem('cart', JSON.stringify(cart));
                
                // Update cart count
                const count = cart.reduce((total, item) => total + item.quantity, 0);
                document.querySelector('.cart-count').textContent = count;
                debugLog(`Cart count updated to ${count}`);
            }
        };

        // Monitor all click events
        document.addEventListener('click', function(event) {
            if (event.target.closest('.add-to-cart-btn')) {
                debugLog('Click detected on add-to-cart-btn');
            }
        });

        debugLog('Debug script loaded');
    </script>
    
    <script src="frontend/js/app.js"></script>
</body>
</html>
