const express = require('express');
const router = express.Router();
const { query, pool } = require('../config/db');
const { authenticateJWT, isAdmin } = require('../middleware/auth');

/**
 * @route   POST /api/orders
 * @desc    Create a new order
 * @access  Private
 */
router.post('/', authenticateJWT, async (req, res) => {
  // Start transaction
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { shipping_address } = req.body;
    
    if (!shipping_address) {
      return res.status(400).json({
        success: false,
        message: 'Shipping address is required'
      });
    }
    
    // Get user's cart
    const cartResult = await client.query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    if (cartResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Get cart items
    const cartItemsResult = await client.query(`
      SELECT ci.*, p.price, p.stock_quantity, p.name
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.product_id
      WHERE ci.cart_id = $1
    `, [cartId]);
    
    if (cartItemsResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cart is empty'
      });
    }
    
    // Check stock availability
    for (const item of cartItemsResult.rows) {
      if (item.quantity > item.stock_quantity) {
        return res.status(400).json({
          success: false,
          message: `Not enough stock available for ${item.name}`
        });
      }
    }
    
    // Calculate total
    let totalAmount = 0;
    cartItemsResult.rows.forEach(item => {
      totalAmount += parseFloat(item.price) * item.quantity;
    });
    
    // Create order
    const orderResult = await client.query(
      `INSERT INTO orders 
       (user_id, total_amount, status, shipping_address, payment_status) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING *`,
      [req.user.user_id, totalAmount, 'pending', shipping_address, 'pending']
    );
    
    const order = orderResult.rows[0];
    
    // Create order items and update stock
    for (const item of cartItemsResult.rows) {
      // Create order item
      await client.query(
        `INSERT INTO order_items 
         (order_id, product_id, quantity, unit_price) 
         VALUES ($1, $2, $3, $4)`,
        [order.order_id, item.product_id, item.quantity, item.price]
      );
      
      // Update product stock
      await client.query(
        'UPDATE products SET stock_quantity = stock_quantity - $1 WHERE product_id = $2',
        [item.quantity, item.product_id]
      );
    }
    
    // Clear cart
    await client.query(
      'DELETE FROM cart_items WHERE cart_id = $1',
      [cartId]
    );
    
    // Commit transaction
    await client.query('COMMIT');
    
    res.status(201).json({
      success: true,
      order: {
        order_id: order.order_id,
        order_date: order.order_date,
        total_amount: order.total_amount,
        status: order.status,
        payment_status: order.payment_status
      },
      message: 'Order created successfully'
    });
  } catch (error) {
    // Rollback transaction on error
    await client.query('ROLLBACK');
    
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating order'
    });
  } finally {
    // Release client
    client.release();
  }
});

/**
 * @route   GET /api/orders
 * @desc    Get user's orders
 * @access  Private
 */
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const result = await query(`
      SELECT o.*, 
             (SELECT COUNT(*) FROM order_items WHERE order_id = o.order_id) as item_count
      FROM orders o
      WHERE o.user_id = $1
      ORDER BY o.order_date DESC
    `, [req.user.user_id]);
    
    res.json({
      success: true,
      orders: result.rows
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching orders'
    });
  }
});

/**
 * @route   GET /api/orders/:id
 * @desc    Get order details
 * @access  Private
 */
router.get('/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get order
    const orderResult = await query(`
      SELECT o.*
      FROM orders o
      WHERE o.order_id = $1 AND (o.user_id = $2 OR $3 = true)
    `, [id, req.user.user_id, req.user.username === 'admin']);
    
    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }
    
    const order = orderResult.rows[0];
    
    // Get order items
    const orderItemsResult = await query(`
      SELECT oi.*, p.name, p.description,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM order_items oi
      JOIN products p ON oi.product_id = p.product_id
      WHERE oi.order_id = $1
    `, [id]);
    
    // Get user details
    const userResult = await query(
      'SELECT user_id, username, email, first_name, last_name FROM users WHERE user_id = $1',
      [order.user_id]
    );
    
    res.json({
      success: true,
      order: {
        ...order,
        items: orderItemsResult.rows,
        user: userResult.rows[0]
      }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching order'
    });
  }
});

/**
 * @route   PUT /api/orders/:id
 * @desc    Update order status (admin only)
 * @access  Private/Admin
 */
router.put('/:id', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, payment_status } = req.body;
    
    if (!status && !payment_status) {
      return res.status(400).json({
        success: false,
        message: 'Status or payment status is required'
      });
    }
    
    // Check if order exists
    const checkResult = await query(
      'SELECT * FROM orders WHERE order_id = $1',
      [id]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }
    
    let updateQuery = 'UPDATE orders SET ';
    const updateValues = [];
    let paramCount = 1;
    
    if (status) {
      updateQuery += `status = $${paramCount}`;
      updateValues.push(status);
      paramCount++;
    }
    
    if (payment_status) {
      if (paramCount > 1) {
        updateQuery += ', ';
      }
      updateQuery += `payment_status = $${paramCount}`;
      updateValues.push(payment_status);
      paramCount++;
    }
    
    updateQuery += ` WHERE order_id = $${paramCount} RETURNING *`;
    updateValues.push(id);
    
    const result = await query(updateQuery, updateValues);
    
    res.json({
      success: true,
      order: result.rows[0],
      message: 'Order updated successfully'
    });
  } catch (error) {
    console.error('Update order error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating order'
    });
  }
});

/**
 * @route   GET /api/orders/admin/all
 * @desc    Get all orders (admin only)
 * @access  Private/Admin
 */
router.get('/admin/all', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;
    
    let queryText = `
      SELECT o.*, 
             u.username, u.first_name, u.last_name,
             (SELECT COUNT(*) FROM order_items WHERE order_id = o.order_id) as item_count
      FROM orders o
      JOIN users u ON o.user_id = u.user_id
      WHERE 1=1
    `;
    
    const queryParams = [];
    let paramCount = 1;
    
    if (status) {
      queryText += ` AND o.status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }
    
    // Add sorting and pagination
    queryText += ` ORDER BY o.order_date DESC LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
    
    const offset = (page - 1) * limit;
    queryParams.push(limit, offset);
    
    const result = await query(queryText, queryParams);
    
    // Get total count for pagination
    let countQueryText = `
      SELECT COUNT(*) as total 
      FROM orders o
      WHERE 1=1
    `;
    
    const countQueryParams = [];
    let countParamCount = 1;
    
    if (status) {
      countQueryText += ` AND o.status = $${countParamCount}`;
      countQueryParams.push(status);
    }
    
    const countResult = await query(countQueryText, countQueryParams);
    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / limit);
    
    res.json({
      success: true,
      orders: result.rows,
      pagination: {
        total_items: totalItems,
        total_pages: totalPages,
        current_page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get all orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching orders'
    });
  }
});

module.exports = router; 