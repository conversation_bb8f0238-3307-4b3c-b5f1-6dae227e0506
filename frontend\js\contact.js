// Contact form functionality for PhoneHub
document.addEventListener('DOMContentLoaded', function() {
    // Contact form submission
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            const subscribe = document.getElementById('subscribe').checked;
            
            // Normally, we would send this data to a server
            // For demo purposes, we'll just show a success message
            console.log('Form submitted:', { name, email, phone, subject, message, subscribe });
            
            // Show success message using the notification system from app.js
            if (typeof showNotification === 'function') {
                showNotification('Thank you for your message! We will get back to you soon.', 'success');
            } else {
                alert('Thank you for your message! We will get back to you soon.');
            }
            
            // Reset form
            contactForm.reset();
        });
    }
    
    // FAQ accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            // Toggle active class on the item
            item.classList.toggle('active');
            
            // Update the icon
            const icon = question.querySelector('.faq-toggle i');
            if (item.classList.contains('active')) {
                icon.classList.replace('fa-plus', 'fa-minus');
            } else {
                icon.classList.replace('fa-minus', 'fa-plus');
            }
        });
    });
    
    // Map placeholder - could be replaced with actual Google Maps integration
    const mapPlaceholder = document.querySelector('.map-placeholder');
    
    if (mapPlaceholder) {
        mapPlaceholder.addEventListener('click', function() {
            if (typeof showNotification === 'function') {
                showNotification('Google Maps integration would be implemented here in a production environment.', 'info');
            }
        });
    }
});