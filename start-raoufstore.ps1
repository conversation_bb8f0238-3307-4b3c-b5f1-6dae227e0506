# RaoufStore E-Commerce - Système Complet
Write-Host "🚀 Démarrage de RaoufStore E-Commerce..." -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js requis. Installez Node.js d'abord." -ForegroundColor Red
    Write-Host "   Téléchargez depuis: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check if we can start without PostgreSQL (using localStorage)
Write-Host "📊 Mode de fonctionnement: localStorage (pas de PostgreSQL requis)" -ForegroundColor Yellow

# Install dependencies if needed
Write-Host "📦 Vérification des dépendances..." -ForegroundColor Yellow
if (Test-Path "backend") {
    Set-Location backend
    if (!(Test-Path "node_modules")) {
        Write-Host "Installation des dépendances backend..." -ForegroundColor Yellow
        npm install
    }
    Set-Location ..
}

Write-Host ""
Write-Host "🔧 Démarrage des serveurs..." -ForegroundColor Cyan

# Start frontend server (main server)
Write-Host "🌐 Démarrage du serveur frontend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "
    Write-Host '🌐 RAOUFSTORE - SERVEUR PRINCIPAL' -ForegroundColor Cyan
    Write-Host 'Port: 8080' -ForegroundColor White
    Write-Host 'Site: http://localhost:8080' -ForegroundColor White
    Write-Host 'Admin: http://localhost:8080/frontend/admin' -ForegroundColor White
    Write-Host 'Mode: localStorage (données locales)' -ForegroundColor White
    Write-Host ''
    node '$PWD\frontend-server.js'
"

# Optional: Start backend if available
if (Test-Path "backend\server.js") {
    Write-Host "🔧 Démarrage du serveur backend (optionnel)..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "
        Write-Host '🔧 BACKEND API - RaoufStore' -ForegroundColor Cyan
        Write-Host 'Port: 3000' -ForegroundColor White
        Write-Host 'API: http://localhost:3000/api' -ForegroundColor White
        Write-Host 'Mode: Serveur simple (sans DB)' -ForegroundColor White
        Write-Host ''
        cd '$PWD\backend'
        node server-simple.js
    "
}

Start-Sleep -Seconds 5

# Test frontend server
Write-Host "🧪 Test du serveur..." -ForegroundColor Yellow

try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 10
    if ($frontendTest.StatusCode -eq 200) {
        Write-Host "✅ RaoufStore: Opérationnel" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend: En cours de démarrage..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    try {
        $frontendTest = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
        if ($frontendTest.StatusCode -eq 200) {
            Write-Host "✅ RaoufStore: Opérationnel" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Problème de démarrage. Vérifiez les logs." -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 RAOUFSTORE E-COMMERCE - SYSTÈME DÉMARRÉ !" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host ""
Write-Host "📱 ACCÈS AU SITE :" -ForegroundColor Cyan
Write-Host "   🌐 Site Principal: http://localhost:8080/frontend/index.html" -ForegroundColor White
Write-Host "   📱 Catalogue: http://localhost:8080/frontend/products.html" -ForegroundColor White
Write-Host "   🛒 Panier: http://localhost:8080/frontend/cart.html" -ForegroundColor White
Write-Host "   🔑 Connexion: http://localhost:8080/frontend/login.html" -ForegroundColor White
Write-Host "   🔧 Admin: http://localhost:8080/frontend/admin/index.html" -ForegroundColor White

Write-Host ""
Write-Host "🧪 PAGES DE TEST :" -ForegroundColor Cyan
Write-Host "   🎨 Test Rebrand: http://localhost:8080/test-raoufstore-rebrand.html" -ForegroundColor White
Write-Host "   🔧 Test Admin: http://localhost:8080/test-admin-features-complete.html" -ForegroundColor White
Write-Host "   🔐 Test Auth: http://localhost:8080/test-auth-system.html" -ForegroundColor White
Write-Host "   🛒 Test Panier: http://localhost:8080/test-cart-fix.html" -ForegroundColor White

Write-Host ""
Write-Host "👤 COMPTES DE CONNEXION :" -ForegroundColor Cyan
Write-Host "   📧 Admin: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Mot de passe: Admin@123" -ForegroundColor White
Write-Host "   📝 Client test: <EMAIL> / User@123" -ForegroundColor White
Write-Host "   ➕ Inscription libre via le site" -ForegroundColor White

Write-Host ""
Write-Host "✨ FONCTIONNALITÉS PRINCIPALES :" -ForegroundColor Cyan
Write-Host "   ✅ E-commerce complet avec panier" -ForegroundColor Green
Write-Host "   ✅ Authentification utilisateur" -ForegroundColor Green
Write-Host "   ✅ Panel d'administration complet" -ForegroundColor Green
Write-Host "   ✅ Gestion stock automatique" -ForegroundColor Green
Write-Host "   ✅ Synchronisation données temps réel" -ForegroundColor Green
Write-Host "   ✅ Suppression utilisateurs (admin)" -ForegroundColor Green
Write-Host "   ✅ Interface moderne et responsive" -ForegroundColor Green

Write-Host ""
Write-Host "🛒 WORKFLOW UTILISATEUR :" -ForegroundColor Cyan
Write-Host "   1. 🏠 Visitez la page d'accueil" -ForegroundColor White
Write-Host "   2. 📱 Parcourez le catalogue produits" -ForegroundColor White
Write-Host "   3. 🔑 Connectez-vous ou inscrivez-vous" -ForegroundColor White
Write-Host "   4. 🛒 Ajoutez des produits au panier" -ForegroundColor White
Write-Host "   5. 💳 Finalisez votre commande" -ForegroundColor White

Write-Host ""
Write-Host "🔧 WORKFLOW ADMIN :" -ForegroundColor Cyan
Write-Host "   1. 🔑 Connectez-vous avec le compte admin" -ForegroundColor White
Write-Host "   2. 📊 Consultez le dashboard" -ForegroundColor White
Write-Host "   3. 👥 Gérez les utilisateurs" -ForegroundColor White
Write-Host "   4. 📦 Gérez les produits et stock" -ForegroundColor White
Write-Host "   5. 🛒 Suivez les commandes" -ForegroundColor White

Write-Host ""
Write-Host "💾 DONNÉES :" -ForegroundColor Cyan
Write-Host "   📊 Stockage: localStorage (navigateur)" -ForegroundColor White
Write-Host "   🔄 Synchronisation: Temps réel" -ForegroundColor White
Write-Host "   💾 Persistance: Automatique" -ForegroundColor White
Write-Host "   🗑️ Reset: Vider le cache navigateur" -ForegroundColor White

Write-Host ""
Write-Host "🛑 ARRÊT :" -ForegroundColor Yellow
Write-Host "   Fermez les fenêtres PowerShell ou appuyez sur Ctrl+C" -ForegroundColor White

Write-Host ""
Write-Host "🌐 Ouverture du site dans le navigateur..." -ForegroundColor Yellow
Start-Process "http://localhost:8080/frontend/index.html"

Write-Host ""
Write-Host "🎊 RaoufStore est prêt ! Bon shopping ! 🛒✨" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan
