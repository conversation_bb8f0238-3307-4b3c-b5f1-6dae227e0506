document.addEventListener('DOMContentLoaded', () => {
    // Load dashboard data
    loadDashboardData();
});

async function loadDashboardData() {
    // Load stats
    loadProductCount();
    loadUserCount();
    loadOrderCount();
    loadRevenue();
    
    // Load charts
    loadSalesChart();
    loadCategoriesChart();
    
    // Load recent orders
    loadRecentOrders();
}

async function loadSalesChart() {
    const salesChartCanvas = document.getElementById('sales-chart');
    if (!salesChartCanvas) return;

    try {
        console.log('📊 Loading sales chart data...');
        // Get sales data from API
        const salesData = await apiRequest('/admin/dashboard');

        if (salesData && salesData.success) {
            console.log('✅ Sales data loaded from API');
            // Use real data if available
            createSalesChartFromData(salesChartCanvas, salesData);
        } else {
            console.log('⚠️ No sales data from API, using sample data');
            // If API fails, use sample data
            createSampleSalesChart(salesChartCanvas);
        }
    } catch (error) {
        console.error('❌ Error loading sales chart:', error);
        createSampleSalesChart(salesChartCanvas);
    }
        
        // Process data for chart
        const labels = salesData.map(item => item.month);
        const data = salesData.map(item => item.amount);
        
        new Chart(salesChartCanvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Sales',
                    data: data,
                    backgroundColor: 'rgba(41, 98, 255, 0.1)',
                    borderColor: '#2962ff',
                    borderWidth: 2,
                    tension: 0.3,
                    pointBackgroundColor: '#2962ff',
                    pointRadius: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error loading sales chart:', error);
        createSampleSalesChart(salesChartCanvas);
    }
}

function createSampleSalesChart(canvas) {
    // Sample data for demonstration
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    const salesData = [12500, 10800, 14300, 16200, 19500, 22800];
    
    new Chart(canvas, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Monthly Sales',
                data: salesData,
                backgroundColor: 'rgba(41, 98, 255, 0.1)',
                borderColor: '#2962ff',
                borderWidth: 2,
                tension: 0.3,
                pointBackgroundColor: '#2962ff',
                pointRadius: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' DZD';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

async function loadCategoriesChart() {
    const categoriesChartCanvas = document.getElementById('categories-chart');
    if (!categoriesChartCanvas) return;
    
    try {
        // Get category data from API
        const categoryData = await apiRequest('/admin/category-sales');
        
        if (!categoryData) {
            // If API fails, use sample data
            createSampleCategoriesChart(categoriesChartCanvas);
            return;
        }
        
        // Process data for chart
        const labels = categoryData.map(item => item.category);
        const data = categoryData.map(item => item.sales);
        
        new Chart(categoriesChartCanvas, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        '#2962ff', 
                        '#4caf50', 
                        '#ff9800', 
                        '#f44336', 
                        '#9c27b0'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error loading categories chart:', error);
        createSampleCategoriesChart(categoriesChartCanvas);
    }
}

function createSampleCategoriesChart(canvas) {
    // Sample data for demonstration
    const categories = ['Smartphones', 'Tablets', 'Accessories', 'Wearables', 'Other'];
    const salesData = [45, 20, 25, 8, 2];
    
    new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: categories,
            datasets: [{
                data: salesData,
                backgroundColor: [
                    '#2962ff', 
                    '#4caf50', 
                    '#ff9800', 
                    '#f44336', 
                    '#9c27b0'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

async function loadRecentOrders() {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;
    
    try {
        // Get recent orders from API
        const orders = await apiRequest('/admin/recent-orders');
        
        if (!orders || !Array.isArray(orders) || orders.length === 0) {
            // If API fails or returns no data, use sample data
            createSampleRecentOrders(recentOrdersContainer);
            return;
        }
        
        // Clear container
        recentOrdersContainer.innerHTML = '';
        
        // Add orders to table
        orders.forEach(order => {
            const tr = document.createElement('tr');
            
            // Determine status class
            let statusClass = '';
            if (order.status === 'completed') {
                statusClass = 'completed';
            } else if (order.status === 'pending') {
                statusClass = 'pending';
            } else if (order.status === 'cancelled') {
                statusClass = 'cancelled';
            }
            
            tr.innerHTML = `
                <td>#${order.order_id}</td>
                <td>${order.customer_name}</td>
                <td>${formatDate(order.order_date)}</td>
                <td>${formatCurrency(order.total)}</td>
                <td><span class="status ${statusClass}">${order.status}</span></td>
                <td><a href="orders.html?id=${order.order_id}" class="btn">View</a></td>
            `;
            
            recentOrdersContainer.appendChild(tr);
        });
    } catch (error) {
        console.error('Error loading recent orders:', error);
        createSampleRecentOrders(recentOrdersContainer);
    }
}

function createSampleRecentOrders(container) {
    // Sample data for demonstration
    const sampleOrders = [
        {
            order_id: 1234,
            customer_name: 'John Doe',
            order_date: '2023-06-25',
            total: 173800,
            status: 'completed'
        },
        {
            order_id: 1233,
            customer_name: 'Jane Smith',
            order_date: '2023-06-24',
            total: 120400,
            status: 'pending'
        },
        {
            order_id: 1232,
            customer_name: 'Bob Johnson',
            order_date: '2023-06-23',
            total: 147000,
            status: 'shipped'
        },
        {
            order_id: 1231,
            customer_name: 'Alice Brown',
            order_date: '2023-06-22',
            total: 20100,
            status: 'cancelled'
        }
    ];
    
    // Clear container
    container.innerHTML = '';
    
    // Add orders to table
    sampleOrders.forEach(order => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>#${order.order_id}</td>
            <td>${order.customer_name}</td>
            <td>${formatDate(order.order_date)}</td>
            <td>${formatCurrency(order.total)}</td>
            <td><span class="status status-${order.status}">${order.status}</span></td>
            <td><a href="orders.html?id=${order.order_id}" class="btn small-btn">View</a></td>
        `;
        
        container.appendChild(tr);
    });
}

async function loadProductCount() {
    const productCountElement = document.getElementById('product-count');
    if (!productCountElement) return;
    
    try {
        const result = await apiRequest('/admin/stats/products');
        if (result && result.count) {
            productCountElement.textContent = result.count;
            return;
        }
    } catch (error) {
        console.error('Error loading product count:', error);
    }
    
    // Fallback to sample data
    productCountElement.textContent = '157';
}

async function loadUserCount() {
    const userCountElement = document.getElementById('user-count');
    if (!userCountElement) return;
    
    try {
        const result = await apiRequest('/admin/stats/users');
        if (result && result.count) {
            userCountElement.textContent = result.count;
            return;
        }
    } catch (error) {
        console.error('Error loading user count:', error);
    }
    
    // Fallback to sample data
    userCountElement.textContent = '842';
}

async function loadOrderCount() {
    const orderCountElement = document.getElementById('order-count');
    if (!orderCountElement) return;
    
    try {
        const result = await apiRequest('/admin/stats/orders');
        if (result && result.count) {
            orderCountElement.textContent = result.count;
            return;
        }
    } catch (error) {
        console.error('Error loading order count:', error);
    }
    
    // Fallback to sample data
    orderCountElement.textContent = '367';
}

async function loadRevenue() {
    const revenueElement = document.getElementById('revenue');
    if (!revenueElement) return;
    
    try {
        const result = await apiRequest('/admin/stats/revenue');
        if (result && result.total) {
            revenueElement.textContent = formatCurrency(result.total);
            return;
        }
    } catch (error) {
        console.error('Error loading revenue:', error);
    }
    
    // Fallback to sample data
    revenueElement.textContent = '11,577,000 DZD';
} 