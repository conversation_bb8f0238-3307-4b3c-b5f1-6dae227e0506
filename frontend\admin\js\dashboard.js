document.addEventListener('DOMContentLoaded', () => {
    // Wait for dependencies to be ready
    if (window.DependencyManager) {
        window.DependencyManager.waitFor(['auth', 'api'], () => {
            loadDashboardData();
        });
    } else {
        // Fallback if dependency manager is not available
        setTimeout(loadDashboardData, 1000);
    }
});

async function loadDashboardData() {
    try {
        // Check if user is authenticated and is admin
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '../login.html';
            return;
        }

        // Load main dashboard data from API
        const dashboardData = await apiRequest('/admin/dashboard');

        if (dashboardData && dashboardData.success) {
            // Update stats with real data
            updateStats(dashboardData.dashboard);

            // Load recent orders
            loadRecentOrdersFromData(dashboardData.dashboard.recent_orders);

            // Load charts with real data
            loadSalesChart();
            loadCategoriesChart();
        } else {
            // If API call fails, show error and use fallback
            console.error('Failed to load dashboard data:', dashboardData);
            loadFallbackData();
        }

    } catch (error) {
        console.error('Dashboard loading error:', error);
        // Check if it's an authentication error
        if (error.message && error.message.includes('401')) {
            localStorage.removeItem('token');
            window.location.href = '../login.html';
            return;
        }
        loadFallbackData();
    }
}

function loadFallbackData() {
    // Load sample data as fallback
    loadProductCount();
    loadUserCount();
    loadOrderCount();
    loadRevenue();
    loadRecentOrders();
    loadSalesChart();
    loadCategoriesChart();
}

// Function to update stats with real data
function updateStats(dashboardData) {
    // Update product count
    const productCountElement = document.getElementById('product-count');
    if (productCountElement && dashboardData.total_products !== undefined) {
        productCountElement.textContent = dashboardData.total_products;
    }

    // Update user count
    const userCountElement = document.getElementById('user-count');
    if (userCountElement && dashboardData.total_users !== undefined) {
        userCountElement.textContent = dashboardData.total_users;
    }

    // Update order count
    const orderCountElement = document.getElementById('order-count');
    if (orderCountElement && dashboardData.total_orders !== undefined) {
        orderCountElement.textContent = dashboardData.total_orders;
    }

    // Update revenue
    const revenueElement = document.getElementById('revenue');
    if (revenueElement && dashboardData.total_sales !== undefined) {
        revenueElement.textContent = formatCurrency(dashboardData.total_sales);
    }
}

async function loadSalesChart() {
    const salesChartCanvas = document.getElementById('sales-chart');
    if (!salesChartCanvas) return;

    try {
        // Get sales data from API
        const salesData = await apiRequest('/admin/sales');

        if (salesData && salesData.success && salesData.sales && salesData.sales.length > 0) {
            // Use real data if available
            createSalesChartFromData(salesChartCanvas, salesData.sales);
        } else {
            // If API fails or no data, use sample data
            createSampleSalesChart(salesChartCanvas);
        }
    } catch (error) {
        console.error('Error loading sales chart:', error);
        createSampleSalesChart(salesChartCanvas);
    }
}

function createSalesChartFromData(canvas, salesData) {
    try {
        // Process data for chart
        const labels = salesData.map(item => item.month);
        const data = salesData.map(item => parseFloat(item.amount) || 0);

        new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Sales',
                    data: data,
                    backgroundColor: 'rgba(41, 98, 255, 0.1)',
                    borderColor: '#2962ff',
                    borderWidth: 2,
                    tension: 0.3,
                    pointBackgroundColor: '#2962ff',
                    pointRadius: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' DZD';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error creating sales chart from data:', error);
        createSampleSalesChart(canvas);
    }
}

function createSampleSalesChart(canvas) {
    // Sample data for demonstration
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    const salesData = [12500, 10800, 14300, 16200, 19500, 22800];

    new Chart(canvas, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Monthly Sales',
                data: salesData,
                backgroundColor: 'rgba(41, 98, 255, 0.1)',
                borderColor: '#2962ff',
                borderWidth: 2,
                tension: 0.3,
                pointBackgroundColor: '#2962ff',
                pointRadius: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' DZD';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

async function loadCategoriesChart() {
    const categoriesChartCanvas = document.getElementById('categories-chart');
    if (!categoriesChartCanvas) return;

    try {
        // Get category data from API
        const categoryData = await apiRequest('/admin/category-sales');

        if (categoryData && categoryData.success && categoryData.category_sales && categoryData.category_sales.length > 0) {
            // Process data for chart
            const labels = categoryData.category_sales.map(item => item.category);
            const data = categoryData.category_sales.map(item => parseFloat(item.sales) || 0);

            new Chart(categoriesChartCanvas, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#2962ff',
                            '#4caf50',
                            '#ff9800',
                            '#f44336',
                            '#9c27b0'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        } else {
            // If API fails or no data, use sample data
            createSampleCategoriesChart(categoriesChartCanvas);
        }
    } catch (error) {
        console.error('Error loading categories chart:', error);
        createSampleCategoriesChart(categoriesChartCanvas);
    }
}

function createSampleCategoriesChart(canvas) {
    // Sample data for demonstration
    const categories = ['Smartphones', 'Tablets', 'Accessories', 'Wearables', 'Other'];
    const salesData = [45, 20, 25, 8, 2];

    new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: categories,
            datasets: [{
                data: salesData,
                backgroundColor: [
                    '#2962ff',
                    '#4caf50',
                    '#ff9800',
                    '#f44336',
                    '#9c27b0'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// Function to load recent orders from dashboard data
function loadRecentOrdersFromData(orders) {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;

    if (!orders || !Array.isArray(orders) || orders.length === 0) {
        createSampleRecentOrders(recentOrdersContainer);
        return;
    }

    // Clear container
    recentOrdersContainer.innerHTML = '';

    // Add orders to table
    orders.forEach(order => {
        const tr = document.createElement('tr');

        // Determine status class
        let statusClass = '';
        if (order.status === 'completed') {
            statusClass = 'completed';
        } else if (order.status === 'pending') {
            statusClass = 'pending';
        } else if (order.status === 'cancelled') {
            statusClass = 'cancelled';
        }

        const customerName = `${order.first_name} ${order.last_name}`;

        tr.innerHTML = `
            <td>#${order.order_id}</td>
            <td>${customerName}</td>
            <td>${formatDate(order.order_date)}</td>
            <td>${formatCurrency(order.total_amount)}</td>
            <td><span class="status ${statusClass}">${order.status}</span></td>
            <td><a href="orders.html?id=${order.order_id}" class="btn">View</a></td>
        `;

        recentOrdersContainer.appendChild(tr);
    });
}

async function loadRecentOrders() {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;

    try {
        // Get recent orders from API
        const ordersData = await apiRequest('/admin/recent-orders');

        if (ordersData && ordersData.success && ordersData.recent_orders) {
            loadRecentOrdersFromData(ordersData.recent_orders);
        } else {
            createSampleRecentOrders(recentOrdersContainer);
        }
    } catch (error) {
        console.error('Error loading recent orders:', error);
        createSampleRecentOrders(recentOrdersContainer);
    }
}

function createSampleRecentOrders(container) {
    // Sample data for demonstration
    const sampleOrders = [
        {
            order_id: 1234,
            customer_name: 'John Doe',
            order_date: '2023-06-25',
            total: 173800,
            status: 'completed'
        },
        {
            order_id: 1233,
            customer_name: 'Jane Smith',
            order_date: '2023-06-24',
            total: 120400,
            status: 'pending'
        },
        {
            order_id: 1232,
            customer_name: 'Bob Johnson',
            order_date: '2023-06-23',
            total: 147000,
            status: 'shipped'
        },
        {
            order_id: 1231,
            customer_name: 'Alice Brown',
            order_date: '2023-06-22',
            total: 20100,
            status: 'cancelled'
        }
    ];

    // Clear container
    container.innerHTML = '';

    // Add orders to table
    sampleOrders.forEach(order => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>#${order.order_id}</td>
            <td>${order.customer_name}</td>
            <td>${formatDate(order.order_date)}</td>
            <td>${formatCurrency(order.total)}</td>
            <td><span class="status status-${order.status}">${order.status}</span></td>
            <td><a href="orders.html?id=${order.order_id}" class="btn small-btn">View</a></td>
        `;

        container.appendChild(tr);
    });
}

async function loadProductCount() {
    const productCountElement = document.getElementById('product-count');
    if (!productCountElement) return;

    try {
        // Try to get from products API
        const result = await apiRequest('/products');
        if (result && result.products) {
            productCountElement.textContent = result.products.length;
            return;
        }
    } catch (error) {
        // Fallback to data manager
        if (window.RaoufStoreData && window.RaoufStoreData.products) {
            productCountElement.textContent = window.RaoufStoreData.products.length;
        } else {
            productCountElement.textContent = '8'; // Default fallback
        }
    }
}

async function loadUserCount() {
    const userCountElement = document.getElementById('user-count');
    if (!userCountElement) return;

    try {
        // Fallback to data manager
        if (window.RaoufStoreData && window.RaoufStoreData.users) {
            userCountElement.textContent = window.RaoufStoreData.users.length;
        } else {
            userCountElement.textContent = '2'; // Default fallback
        }
    } catch (error) {
        userCountElement.textContent = '2';
    }
}

async function loadOrderCount() {
    const orderCountElement = document.getElementById('order-count');
    if (!orderCountElement) return;

    try {
        // Fallback to data manager
        if (window.RaoufStoreData && window.RaoufStoreData.orders) {
            orderCountElement.textContent = window.RaoufStoreData.orders.length;
        } else {
            orderCountElement.textContent = '3'; // Default fallback
        }
    } catch (error) {
        orderCountElement.textContent = '3';
    }
}

async function loadRevenue() {
    const revenueElement = document.getElementById('revenue');
    if (!revenueElement) return;

    try {
        // Calculate revenue from data manager orders
        if (window.RaoufStoreData && window.RaoufStoreData.orders) {
            const totalRevenue = window.RaoufStoreData.orders.reduce((sum, order) => {
                return sum + (order.total || 0);
            }, 0);
            revenueElement.textContent = formatCurrency(totalRevenue);
        } else {
            revenueElement.textContent = formatCurrency(450000); // Default fallback
        }
    } catch (error) {
        revenueElement.textContent = formatCurrency(450000);
    }
}