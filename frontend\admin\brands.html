<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Brands</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li><a href="admin-dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li class="active"><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Brands Management</h1>
                <div class="admin-user">
                    <span>Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="content-actions">
                <div class="search-box">
                    <input type="text" id="brand-search" placeholder="Search brands...">
                    <button><i class="fas fa-search"></i></button>
                </div>
                <button class="btn primary-btn" id="add-brand-btn">
                    <i class="fas fa-plus"></i> Add Brand
                </button>
            </div>
            
            <div class="data-table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Products</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="brands-table-body">
                        <!-- Brands will be loaded here -->
                    </tbody>
                </table>
                
                <div class="pagination">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button class="pagination-btn" id="next-page" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
        
        <!-- Add/Edit Brand Modal -->
        <div class="modal" id="brand-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">Add New Brand</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="brand-form">
                        <input type="hidden" id="brand-id">
                        
                        <div class="form-group">
                            <label for="brand-name">Brand Name</label>
                            <input type="text" id="brand-name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="brand-description">Description</label>
                            <textarea id="brand-description" name="description" rows="4"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="brand-logo">Logo URL</label>
                            <input type="text" id="brand-logo" name="logo_url" placeholder="https://example.com/logos/brand.png">
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn secondary-btn" id="cancel-btn">Cancel</button>
                            <button type="submit" class="btn primary-btn">Save Brand</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div class="modal" id="delete-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Deletion</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this brand? This action cannot be undone.</p>
                    <p class="warning-text">Note: This will affect all products associated with this brand.</p>
                    
                    <div class="form-actions">
                        <button type="button" class="btn secondary-btn" id="cancel-delete-btn">Cancel</button>
                        <button type="button" class="btn danger-btn" id="confirm-delete-btn">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
    <script src="js/brands.js"></script>
</body>
</html> 