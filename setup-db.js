const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Try different common passwords
const passwords = ['postgres', 'admin', 'password', '123456', '', 'r<PERSON><PERSON><PERSON><PERSON>@'];

async function tryConnection(password) {
  const client = new Client({
    user: 'postgres',
    password: password,
    host: 'localhost',
    port: 5432,
    database: 'postgres' // Connect to default database first
  });

  try {
    await client.connect();
    console.log(`✅ Connected with password: "${password}"`);
    return client;
  } catch (error) {
    console.log(`❌ Failed with password: "${password}"`);
    return null;
  }
}

async function setupDatabase() {
  console.log('🔍 Trying to connect to PostgreSQL...');
  
  let client = null;
  
  // Try different passwords
  for (const password of passwords) {
    client = await tryConnection(password);
    if (client) {
      break;
    }
  }
  
  if (!client) {
    console.error('❌ Could not connect to PostgreSQL with any common password');
    console.log('Please check your PostgreSQL installation and password');
    return;
  }
  
  try {
    // Check if database exists
    const dbCheck = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'ecommerce_phones'"
    );
    
    if (dbCheck.rows.length === 0) {
      console.log('📦 Creating database ecommerce_phones...');
      await client.query('CREATE DATABASE ecommerce_phones');
      console.log('✅ Database created successfully');
    } else {
      console.log('✅ Database ecommerce_phones already exists');
    }
    
    await client.end();
    
    // Connect to the new database
    const dbClient = new Client({
      user: 'postgres',
      password: client.password,
      host: 'localhost',
      port: 5432,
      database: 'ecommerce_phones'
    });
    
    await dbClient.connect();
    console.log('✅ Connected to ecommerce_phones database');
    
    // Check if tables exist
    const tableCheck = await dbClient.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
    );
    
    if (tableCheck.rows.length === 0) {
      console.log('📋 Creating database schema...');
      
      // Read and execute schema
      const schemaSQL = fs.readFileSync(path.join(__dirname, 'init-db', '01-schema.sql'), 'utf8');
      await dbClient.query(schemaSQL);
      console.log('✅ Schema created successfully');
      
      console.log('📊 Inserting seed data...');
      const seedSQL = fs.readFileSync(path.join(__dirname, 'init-db', '02-seed-data.sql'), 'utf8');
      await dbClient.query(seedSQL);
      console.log('✅ Seed data inserted successfully');
      
    } else {
      console.log('✅ Database tables already exist');
    }
    
    await dbClient.end();
    console.log('🎉 Database setup completed successfully!');
    
    // Update the .env file with the correct password
    const envContent = `DB_USER=postgres
DB_PASSWORD=${client.password}
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_phones
JWT_SECRET=your_jwt_secret_key_here_${Math.random().toString(36).substring(7)}
NODE_ENV=development`;
    
    fs.writeFileSync(path.join(__dirname, 'backend', '.env'), envContent);
    console.log('✅ Environment file updated');
    
  } catch (error) {
    console.error('❌ Error setting up database:', error.message);
  }
}

setupDatabase();
