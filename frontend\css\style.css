/* Global Styles */
:root {
    --primary-color: #5a55b9;
    --secondary-color: #7f78d2;
    --accent-color: #ff6b6b;
    --text-color: #333333;
    --light-color: #f9f9f9;
    --dark-color: #333333;
    --gray-color: #f0f0f0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 10px;
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: var(--light-color);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
    font-size: 32px;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

/* Header */
header {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo a {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    margin-right: 10px;
    font-size: 28px;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: var(--text-color);
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
}

nav ul li a.active,
nav ul li a:hover {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.auth-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-icon {
    position: relative;
    margin-right: 20px;
    font-size: 20px;
    color: var(--text-color);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--accent-color);
    color: white;
    font-size: 12px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.login-btn:hover {
    background-color: var(--secondary-color);
}

.register-btn {
    background-color: transparent;
    color: var(--primary-color);
    padding: 8px 15px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
}

.register-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

/* User Menu Styles */
.user-menu {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-greeting {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
}

.user-dropdown {
    position: relative;
}

.user-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-color);
}

.user-menu-btn:hover {
    background-color: #f8f9fa;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 4px;
    z-index: 1000;
    border: 1px solid #e1e5eb;
    margin-top: 5px;
}

.dropdown-content.show {
    display: block;
}

.dropdown-content a {
    color: var(--text-color);
    padding: 12px 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.3s;
}

.dropdown-content a:hover {
    background-color: #f8f9fa;
}

.dropdown-content hr {
    margin: 5px 0;
    border: none;
    border-top: 1px solid #e1e5eb;
}

/* Auth Modal Styles */
.auth-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.auth-modal-content {
    background-color: white;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.auth-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
}

.auth-modal-header h3 {
    margin: 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    color: var(--text-color);
}

.auth-modal-body {
    padding: 20px;
}

.auth-modal-body p {
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.5;
}

.auth-modal-actions {
    display: flex;
    gap: 10px;
}

.auth-modal-actions .btn {
    flex: 1;
    text-align: center;
    padding: 10px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
}

/* User Modal Styles */
.user-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.user-modal-content {
    background-color: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid #e1e5eb;
    margin-bottom: 20px;
}

.user-modal-header h3 {
    margin: 0;
    color: var(--text-color);
}

.user-modal-body {
    padding: 0 20px 20px 20px;
}

/* Profile Info Styles */
.profile-info {
    margin-bottom: 20px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-field span {
    color: #6c757d;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-customer {
    background-color: #28a745;
    color: white;
}

.profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.profile-actions .btn {
    flex: 1;
    padding: 10px;
    text-align: center;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover {
    background-color: #c82333;
}

/* Orders Styles */
.orders-container {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    border: 1px solid #e1e5eb;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.order-header {
    background-color: #f8f9fa;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e1e5eb;
}

.order-id {
    font-weight: 600;
    color: var(--primary-color);
}

.order-date {
    color: #6c757d;
    font-size: 14px;
}

.order-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-delivered {
    background-color: #28a745;
    color: white;
}

.status-shipped {
    background-color: #007bff;
    color: white;
}

.status-processing {
    background-color: #ffc107;
    color: #212529;
}

.order-details {
    padding: 15px;
}

.order-item-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.order-item-detail:last-child {
    border-bottom: none;
}

.order-total {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 2px solid #e1e5eb;
    text-align: right;
}

.no-orders {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.orders-actions {
    margin-top: 20px;
    text-align: center;
}

/* Profile Form Styles */
.profile-form {
    max-width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 30px;
    justify-content: flex-end;
}

.form-actions .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 10px;
}

.strength-bar {
    width: 100%;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: width 0.3s, background-color 0.3s;
    border-radius: 3px;
}

.strength-text {
    font-size: 12px;
    color: #6c757d;
}

/* Notification Styles */
.auth-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.auth-notification.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.auth-notification.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.auth-notification.info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.notification-content {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-greeting {
        display: none;
    }

    .dropdown-content {
        min-width: 180px;
    }

    .auth-modal-actions {
        flex-direction: column;
    }

    .user-modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .profile-actions {
        flex-direction: column;
    }

    .order-header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .order-item-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* Hero Section */
.hero {
    background: url('../images/hero-bg.svg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 120px 0;
    text-align: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.hero-content {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 48px;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
}

.hero p {
    font-size: 20px;
    margin-bottom: 40px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 1s ease-out 0.3s forwards;
    opacity: 0;
}

.hero .btn {
    padding: 14px 30px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 1s ease-out 0.6s forwards;
    opacity: 0;
    background-color: var(--accent-color);
    border: 2px solid var(--accent-color);
}

.hero .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background-color: transparent;
    color: white;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Featured Categories */
.featured-categories {
    padding: 80px 0;
    background-color: #fff;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.category-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px;
    height: 350px;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.category-card img {
    height: 200px;
    object-fit: contain;
    margin-bottom: 20px;
    transition: transform var(--transition-speed);
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--dark-color);
    font-weight: 600;
}

.category-card .btn {
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 30px;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.category-card .btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Featured Products */
.featured-products {
    padding: 60px 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 30px;
}

.product-card {
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.product-image {
    height: 250px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    padding: 20px;
}

.product-image img {
    max-height: 100%;
    object-fit: contain;
    transition: transform var(--transition-speed);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    border-radius: 4px;
    z-index: 1;
}

.tag-new {
    background-color: var(--primary-color);
}

.tag-sale {
    background-color: var(--accent-color);
}

.product-info {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-info h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--dark-color);
    line-height: 1.4;
    font-weight: 600;
}

.product-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.price-old {
    text-decoration: line-through;
    color: #999;
    font-size: 14px;
    margin-right: 8px;
}

.product-rating {
    margin-bottom: 15px;
    font-size: 14px;
    color: #ffa41c;
    display: flex;
    align-items: center;
}

.product-rating span {
    margin-right: 8px;
}

.rating-count {
    color: #999;
    font-size: 12px;
}

.product-actions {
    padding: 15px 20px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 10px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
}

.product-actions .btn {
    padding: 8px 12px;
    font-size: 14px;
}

.product-actions .primary-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.view-all-container {
    text-align: center;
    margin-top: 30px;
}

/* Promotions */
.promotions {
    padding: 70px 0;
    background-color: #f9f9f9;
}

.promotion-banner {
    background: linear-gradient(135deg, rgba(90, 85, 185, 0.9) 0%, rgba(127, 120, 210, 0.9) 100%), url('../images/promo-bg.svg');
    background-size: cover;
    background-position: center;
    border-radius: var(--border-radius);
    padding: 60px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.promotion-banner::before {
    content: '';
    position: absolute;
    top: -30px;
    right: -30px;
    width: 150px;
    height: 150px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.promotion-banner::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.promotion-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
}

.promotion-content h2 {
    font-size: 36px;
    margin-bottom: 20px;
    font-weight: 700;
}

.promotion-content p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.promotion-content .btn {
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 30px;
    background-color: white;
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

.promotion-content .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    margin-bottom: 20px;
    font-size: 18px;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #bbbbbb;
    transition: color 0.3s;
}

.footer-column ul li a:hover {
    color: white;
}

.footer-column p {
    color: #bbbbbb;
    margin-bottom: 10px;
}

.footer-bottom {
    border-top: 1px solid #444444;
    padding-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #bbbbbb;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        padding: 15px;
    }
    
    nav {
        margin: 15px 0;
    }
    
    .hero {
        padding: 60px 0;
    }
    
    .hero h1 {
        font-size: 32px;
    }
    
    .promotion-banner {
        padding: 30px;
    }
} 