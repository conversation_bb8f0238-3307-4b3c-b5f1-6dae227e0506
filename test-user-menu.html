<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Menu - PhoneHub</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .test-button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>PhoneHub</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="frontend/login.html" class="login-btn">Login</a>
                    <a href="frontend/login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <div class="test-container">
        <h1>👤 Test du Menu Utilisateur</h1>
        
        <div class="test-section">
            <h2>📊 État de Connexion</h2>
            <div id="auth-status" class="status warning">Vérification en cours...</div>
            <div id="user-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🔑 Connexion/Déconnexion</h2>
            <button class="test-button" onclick="loginAsAdmin()">🔑 Se connecter comme Admin</button>
            <button class="test-button" onclick="loginAsCustomer()">👤 Se connecter comme Client</button>
            <button class="test-button danger" onclick="logout()">🚪 Se déconnecter</button>
        </div>
        
        <div class="test-section">
            <h2>👤 Tests du Menu Utilisateur</h2>
            <p><strong>Instructions :</strong> Connectez-vous d'abord, puis testez les boutons du menu utilisateur dans l'en-tête.</p>
            
            <div id="menu-tests" style="display: none;">
                <p class="status success">✅ Utilisateur connecté ! Vous devriez voir le menu utilisateur dans l'en-tête.</p>
                <p><strong>Testez ces fonctionnalités :</strong></p>
                <ul>
                    <li>Cliquez sur le bouton utilisateur (icône + flèche) dans l'en-tête</li>
                    <li>Cliquez sur "Profile" pour voir les informations utilisateur</li>
                    <li>Cliquez sur "My Orders" pour voir l'historique des commandes</li>
                    <li>Cliquez sur "Logout" pour se déconnecter</li>
                </ul>
                
                <h4>🧪 Tests Directs :</h4>
                <button class="test-button" onclick="testProfile()">👤 Test Profile</button>
                <button class="test-button" onclick="testOrders()">📦 Test My Orders</button>
                <button class="test-button" onclick="testDropdown()">📋 Test Dropdown</button>
            </div>
            
            <div id="login-prompt" style="display: block;">
                <p class="status warning">⚠️ Veuillez vous connecter pour tester le menu utilisateur.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Fonctionnalités Testées</h2>
            <ul id="feature-checklist">
                <li id="check-login">❓ Connexion utilisateur</li>
                <li id="check-menu">❓ Affichage du menu utilisateur</li>
                <li id="check-profile">❓ Modal Profile fonctionnel</li>
                <li id="check-orders">❓ Modal My Orders fonctionnel</li>
                <li id="check-dropdown">❓ Dropdown menu fonctionnel</li>
                <li id="check-logout">❓ Déconnexion fonctionnelle</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📝 Journal des Tests</h2>
            <div id="test-log" style="background: white; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
            <button class="test-button" onclick="clearLog()">🗑️ Vider le Journal</button>
        </div>
        
        <div class="test-section">
            <h2>🔗 Navigation</h2>
            <a href="http://localhost:8080" class="test-button">🏠 Site Principal</a>
            <a href="http://localhost:8080/test-auth-system.html" class="test-button">🔐 Test Auth</a>
            <a href="http://localhost:8080/frontend/login.html" class="test-button">🔑 Page Login</a>
        </div>
    </div>

    <script src="frontend/js/auth.js"></script>
    <script src="frontend/js/app.js"></script>
    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[USER MENU TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateStatus() {
            const statusElement = document.getElementById('auth-status');
            const userInfoElement = document.getElementById('user-info');
            const menuTests = document.getElementById('menu-tests');
            const loginPrompt = document.getElementById('login-prompt');
            
            if (window.authManager && window.authManager.isAuthenticated()) {
                const user = window.authManager.getCurrentUser();
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Utilisateur connecté';
                userInfoElement.innerHTML = `
                    <strong>Nom :</strong> ${user.first_name} ${user.last_name}<br>
                    <strong>Email :</strong> ${user.email}<br>
                    <strong>Rôle :</strong> ${user.role}
                `;
                menuTests.style.display = 'block';
                loginPrompt.style.display = 'none';
                updateChecklist('check-login', true);
                updateChecklist('check-menu', true);
                log(`Utilisateur connecté: ${user.email}`);
            } else {
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ Utilisateur non connecté';
                userInfoElement.innerHTML = '<em>Aucun utilisateur connecté</em>';
                menuTests.style.display = 'none';
                loginPrompt.style.display = 'block';
                updateChecklist('check-login', false);
                updateChecklist('check-menu', false);
                log('Aucun utilisateur connecté');
            }
        }

        function updateChecklist(id, success) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = element.textContent.replace(/[❓✅❌]/, success ? '✅' : '❌');
            }
        }

        function loginAsAdmin() {
            log('Connexion comme Admin...');
            const token = btoa('<EMAIL>:admin:' + Date.now());
            const userInfo = {
                user_id: 1,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                created_at: '2023-01-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                log('Connexion Admin réussie');
                setTimeout(updateStatus, 100);
            }
        }

        function loginAsCustomer() {
            log('Connexion comme Client...');
            const token = btoa('<EMAIL>:customer:' + Date.now());
            const userInfo = {
                user_id: 2,
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                role: 'customer',
                created_at: '2023-06-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                log('Connexion Client réussie');
                setTimeout(updateStatus, 100);
            }
        }

        function logout() {
            log('Déconnexion...');
            if (window.authManager) {
                window.authManager.logout();
                log('Déconnexion réussie');
                updateChecklist('check-logout', true);
                setTimeout(updateStatus, 100);
            }
        }

        function testProfile() {
            log('Test du modal Profile...');
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.showUserProfile();
                updateChecklist('check-profile', true);
                log('Modal Profile ouvert avec succès');
            } else {
                log('❌ Utilisateur non connecté');
            }
        }

        function testOrders() {
            log('Test du modal My Orders...');
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.showUserOrders();
                updateChecklist('check-orders', true);
                log('Modal My Orders ouvert avec succès');
            } else {
                log('❌ Utilisateur non connecté');
            }
        }

        function testDropdown() {
            log('Test du dropdown menu...');
            const userMenuBtn = document.querySelector('.user-menu-btn');
            if (userMenuBtn) {
                userMenuBtn.click();
                updateChecklist('check-dropdown', true);
                log('Dropdown menu activé');
            } else {
                log('❌ Bouton menu utilisateur non trouvé');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Page de test du menu utilisateur chargée');
            setTimeout(updateStatus, 500);
            
            // Listen for auth events
            window.addEventListener('userLoggedIn', (event) => {
                log(`Événement: Utilisateur connecté - ${event.detail.email}`);
                setTimeout(updateStatus, 100);
            });
            
            window.addEventListener('userLoggedOut', () => {
                log('Événement: Utilisateur déconnecté');
                setTimeout(updateStatus, 100);
            });
        });
    </script>
</body>
</html>
