const http = require('http');

function testAPI(endpoint) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: endpoint,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data, error: 'Invalid JSON' });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing API endpoints...\n');

  // Test health endpoint
  try {
    console.log('1. Testing /api/health...');
    const healthResult = await testAPI('/api/health');
    console.log('✅ Health Status:', healthResult.status);
    console.log('📦 Health Response:', healthResult.data);
    console.log('');
  } catch (error) {
    console.log('❌ Health test failed:', error.message);
  }

  // Test products endpoint
  try {
    console.log('2. Testing /api/products...');
    const productsResult = await testAPI('/api/products');
    console.log('✅ Products Status:', productsResult.status);
    if (productsResult.data.success) {
      console.log('📦 Products Count:', productsResult.data.products?.length || 0);
      console.log('📦 First Product:', productsResult.data.products?.[0]?.name || 'None');
    } else {
      console.log('❌ Products Error:', productsResult.data.message);
    }
    console.log('');
  } catch (error) {
    console.log('❌ Products test failed:', error.message);
  }

  // Test featured products endpoint
  try {
    console.log('3. Testing /api/products/featured...');
    const featuredResult = await testAPI('/api/products/featured');
    console.log('✅ Featured Status:', featuredResult.status);
    if (featuredResult.data.success) {
      console.log('📦 Featured Count:', featuredResult.data.featured_products?.length || 0);
    } else {
      console.log('❌ Featured Error:', featuredResult.data.message);
    }
    console.log('');
  } catch (error) {
    console.log('❌ Featured test failed:', error.message);
  }

  console.log('🎉 API tests completed!');
}

runTests();
