// About Us Page JavaScript
document.addEventListener('DOMContentLoaded', () => {
    // Testimonials carousel
    const testimonials = document.querySelectorAll('.testimonial');
    const prevBtn = document.querySelector('.testimonial-btn.prev');
    const nextBtn = document.querySelector('.testimonial-btn.next');
    let currentTestimonial = 0;
    const testimonialCount = testimonials.length;

    // Initialize
    updateTestimonialDisplay();

    // Event listeners
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            currentTestimonial = (currentTestimonial - 1 + testimonialCount) % testimonialCount;
            updateTestimonialDisplay();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentTestimonial = (currentTestimonial + 1) % testimonialCount;
            updateTestimonialDisplay();
        });
    }

    // Auto-rotate testimonials every 8 seconds
    setInterval(() => {
        currentTestimonial = (currentTestimonial + 1) % testimonialCount;
        updateTestimonialDisplay();
    }, 8000);

    // Update testimonial display
    function updateTestimonialDisplay() {
        testimonials.forEach((testimonial, index) => {
            const position = (index - currentTestimonial + testimonialCount) % testimonialCount;
            testimonial.style.transform = `translateX(${position * 100}%)`;
            testimonial.style.zIndex = testimonialCount - position;
        });
    }

    // Animate value icons on scroll
    const valueCards = document.querySelectorAll('.value-card');
    const valueIcons = document.querySelectorAll('.value-icon');
    
    // Set up Intersection Observer for value icons
    const iconObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                // Stop observing once animated
                iconObserver.unobserve(entry.target);
            }
        });
    }, {
        root: null,
        threshold: 0.5,
        rootMargin: '0px'
    });
    
    // Observe each value icon
    valueIcons.forEach(icon => {
        iconObserver.observe(icon);
    });

    // Add simple hover effect to team members
    const teamMembers = document.querySelectorAll('.team-member');
    
    teamMembers.forEach(member => {
        member.addEventListener('mouseenter', () => {
            member.style.backgroundColor = '#f9f9f9';
        });
        
        member.addEventListener('mouseleave', () => {
            member.style.backgroundColor = 'white';
        });
    });

    // Add CSS for icon animation
    const style = document.createElement('style');
    style.textContent = `
        .value-icon {
            transform: scale(0.8);
            opacity: 0.8;
            transition: transform 0.5s, opacity 0.5s;
        }
        
        .value-icon.animate {
            transform: scale(1);
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}); 