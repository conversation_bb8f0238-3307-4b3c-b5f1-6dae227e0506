<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Orders</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li class="active"><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Orders Management</h1>
                <div class="admin-user">
                    <span>Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="content-actions">
                <div class="search-box">
                    <input type="text" id="order-search" placeholder="Search orders by ID or customer...">
                    <button><i class="fas fa-search"></i></button>
                </div>
                <div class="filter-group">
                    <select id="status-filter">
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>
            
            <div class="data-table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Payment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="orders-table-body">
                        <!-- Orders will be loaded here -->
                    </tbody>
                </table>
                
                <div class="pagination">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button class="pagination-btn" id="next-page" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h2 id="modal-title">Order Details</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="order-details">
                        <div class="order-info">
                            <h3>Order Information</h3>
                            <div class="info-group">
                                <p><strong>Order ID:</strong> <span id="detail-order-id"></span></p>
                                <p><strong>Date:</strong> <span id="detail-date"></span></p>
                                <p><strong>Status:</strong> <span id="detail-status"></span></p>
                                <p><strong>Payment Status:</strong> <span id="detail-payment"></span></p>
                            </div>
                        </div>
                        
                        <div class="customer-info">
                            <h3>Customer Information</h3>
                            <div class="info-group">
                                <p><strong>Name:</strong> <span id="detail-customer-name"></span></p>
                                <p><strong>Email:</strong> <span id="detail-customer-email"></span></p>
                                <p><strong>Shipping Address:</strong> <span id="detail-shipping-address"></span></p>
                            </div>
                        </div>
                        
                        <div class="order-items">
                            <h3>Order Items</h3>
                            <table class="detail-table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                        <th>Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody id="order-items-body">
                                    <!-- Order items will be loaded here -->
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-right"><strong>Total:</strong></td>
                                        <td id="detail-total"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="update-status">
                            <h3>Update Status</h3>
                            <form id="update-status-form">
                                <div class="form-group">
                                    <label for="status-select">Order Status</label>
                                    <select id="status-select" name="status">
                                        <option value="pending">Pending</option>
                                        <option value="processing">Processing</option>
                                        <option value="shipped">Shipped</option>
                                        <option value="delivered">Delivered</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn primary-btn">Update Status</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/data-manager.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
    <script src="js/orders.js"></script>
</body>
</html> 