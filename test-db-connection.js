const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  password: 'r<PERSON><PERSON><PERSON><PERSON>@',
  host: 'localhost',
  port: 5432,
  database: 'ecommerce_phones'
});

async function testConnection() {
  try {
    console.log('🔄 Testing database connection...');
    console.log('Database config:', {
      user: 'postgres',
      host: 'localhost',
      port: 5432,
      database: 'ecommerce_phones'
    });
    
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL database');
    
    // Test query
    const result = await client.query('SELECT NOW()');
    console.log('✅ Database query successful:', result.rows[0]);
    
    // Check if tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log('📋 Available tables:', tablesResult.rows.map(row => row.table_name));
    
    // Check products table
    try {
      const productsResult = await client.query('SELECT COUNT(*) FROM products');
      console.log('📦 Products count:', productsResult.rows[0].count);
    } catch (error) {
      console.log('⚠️ Products table not found or empty');
    }
    
    client.release();
    console.log('✅ Database connection test completed');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await pool.end();
  }
}

testConnection();
