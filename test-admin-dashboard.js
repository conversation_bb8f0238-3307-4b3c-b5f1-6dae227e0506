const http = require('http');

// First, login to get admin token
function loginAdmin() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'Admin@123'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/users/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          if (jsonData.success && jsonData.token) {
            resolve(jsonData.token);
          } else {
            reject(new Error('Login failed'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

// Test dashboard API
function testDashboard(token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/admin/dashboard',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        console.log('📊 Dashboard API Response:');
        console.log('Status Code:', res.statusCode);
        console.log('Response:', data);
        resolve(data);
      });
    });

    req.on('error', reject);
    req.end();
  });
}

async function testAdminDashboard() {
  try {
    console.log('🔐 Logging in as admin...');
    const token = await loginAdmin();
    console.log('✅ Admin login successful!');
    
    console.log('\n📊 Testing dashboard API...');
    await testDashboard(token);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAdminDashboard();
