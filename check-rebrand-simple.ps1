# Script pour verifier le rebrand PhoneHub -> RaoufStore
Write-Host "Verification du rebrand PhoneHub -> RaoufStore..." -ForegroundColor Cyan

$foundPhoneHub = $false

# Fichiers a verifier
$filesToCheck = @(
    "frontend\index.html",
    "frontend\login.html",
    "frontend\admin\index.html",
    "frontend\admin\login.html",
    "frontend\js\data-manager.js"
)

Write-Host "Verification des fichiers principaux..." -ForegroundColor Yellow

foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        
        if ($content -match "PhoneHub") {
            Write-Host "ERREUR: $file contient encore 'PhoneHub'" -ForegroundColor Red
            $foundPhoneHub = $true
        } else {
            Write-Host "OK: $file ne contient plus 'PhoneHub'" -ForegroundColor Green
        }
    }
}

if (-not $foundPhoneHub) {
    Write-Host "SUCCES: Rebrand complete!" -ForegroundColor Green
} else {
    Write-Host "ERREUR: Rebrand incomplet" -ForegroundColor Red
}

Write-Host "Test: http://localhost:8080/frontend/index.html" -ForegroundColor White
