<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Edit - PhoneHub</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .test-button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.danger { background: #dc3545; }
        .test-button.warning { background: #ffc107; color: #212529; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .feature-demo { background: white; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <!-- Header with auth -->
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>PhoneHub</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="frontend/login.html" class="login-btn">Login</a>
                    <a href="frontend/login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <div class="test-container">
        <h1>✏️ Test Edit Profile & Change Password</h1>
        
        <div class="test-section">
            <h2>📊 État de Connexion</h2>
            <div id="auth-status" class="status warning">Vérification en cours...</div>
            <div id="user-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🔑 Connexion Rapide</h2>
            <button class="test-button success" onclick="loginAsAdmin()">
                <i class="fas fa-user-shield"></i> Login Admin
            </button>
            <button class="test-button" onclick="loginAsCustomer()">
                <i class="fas fa-user"></i> Login Client
            </button>
            <button class="test-button danger" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
        
        <div class="test-section" id="profile-tests" style="display: none;">
            <h2>✏️ Tests Edit Profile</h2>
            <div class="feature-demo">
                <h4><i class="fas fa-edit"></i> Fonctionnalités du formulaire Edit Profile :</h4>
                <ul>
                    <li>✅ Modification du prénom et nom</li>
                    <li>✅ Modification de l'email</li>
                    <li>✅ Ajout/modification du téléphone</li>
                    <li>✅ Ajout/modification de l'adresse</li>
                    <li>✅ Validation des champs requis</li>
                    <li>✅ Sauvegarde des modifications</li>
                    <li>✅ Mise à jour de l'interface</li>
                </ul>
            </div>
            
            <button class="test-button" onclick="testEditProfile()">
                <i class="fas fa-edit"></i> Test Edit Profile
            </button>
            
            <div id="edit-profile-status" class="status success" style="display: none;">
                ✅ Formulaire Edit Profile ouvert ! Testez les modifications et la sauvegarde.
            </div>
        </div>
        
        <div class="test-section" id="password-tests" style="display: none;">
            <h2>🔐 Tests Change Password</h2>
            <div class="feature-demo">
                <h4><i class="fas fa-key"></i> Fonctionnalités du formulaire Change Password :</h4>
                <ul>
                    <li>✅ Vérification du mot de passe actuel</li>
                    <li>✅ Indicateur de force du nouveau mot de passe</li>
                    <li>✅ Vérification de la confirmation</li>
                    <li>✅ Validation longueur minimum (6 caractères)</li>
                    <li>✅ Messages d'erreur explicites</li>
                    <li>✅ Notification de succès</li>
                </ul>
            </div>
            
            <button class="test-button warning" onclick="testChangePassword()">
                <i class="fas fa-key"></i> Test Change Password
            </button>
            
            <div id="change-password-status" class="status success" style="display: none;">
                ✅ Formulaire Change Password ouvert ! Testez la validation et l'indicateur de force.
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Tests Spécifiques</h2>
            <div class="feature-demo">
                <h4>Scénarios de test recommandés :</h4>
                <ol>
                    <li><strong>Edit Profile :</strong>
                        <ul>
                            <li>Modifiez le prénom et sauvegardez</li>
                            <li>Essayez de vider un champ requis</li>
                            <li>Ajoutez un numéro de téléphone</li>
                            <li>Vérifiez que les changements persistent</li>
                        </ul>
                    </li>
                    <li><strong>Change Password :</strong>
                        <ul>
                            <li>Testez avec un mot de passe trop court</li>
                            <li>Testez avec des mots de passe non correspondants</li>
                            <li>Observez l'indicateur de force du mot de passe</li>
                            <li>Testez un changement valide</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Checklist des Fonctionnalités</h2>
            <ul id="feature-checklist">
                <li id="check-login">❓ Connexion utilisateur</li>
                <li id="check-edit-form">❓ Formulaire Edit Profile</li>
                <li id="check-edit-save">❓ Sauvegarde modifications profil</li>
                <li id="check-password-form">❓ Formulaire Change Password</li>
                <li id="check-password-strength">❓ Indicateur force mot de passe</li>
                <li id="check-password-validation">❓ Validation mot de passe</li>
                <li id="check-notifications">❓ Notifications de succès/erreur</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📝 Journal des Tests</h2>
            <div id="test-log" style="background: white; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
            <button class="test-button" onclick="clearLog()">🗑️ Vider le Journal</button>
        </div>
        
        <div class="test-section">
            <h2>🔗 Navigation</h2>
            <a href="http://localhost:8080" class="test-button">🏠 Site Principal</a>
            <a href="http://localhost:8080/test-user-menu.html" class="test-button">👤 Test Menu</a>
            <a href="http://localhost:8080/quick-test-user-menu.html" class="test-button">⚡ Test Rapide</a>
        </div>
    </div>

    <script src="frontend/js/auth.js"></script>
    <script src="frontend/js/app.js"></script>
    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[PROFILE EDIT TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateChecklist(id, success) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = element.textContent.replace(/[❓✅❌]/, success ? '✅' : '❌');
            }
        }

        function updateStatus() {
            const statusElement = document.getElementById('auth-status');
            const userInfoElement = document.getElementById('user-info');
            const profileTests = document.getElementById('profile-tests');
            const passwordTests = document.getElementById('password-tests');
            
            if (window.authManager && window.authManager.isAuthenticated()) {
                const user = window.authManager.getCurrentUser();
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Utilisateur connecté';
                userInfoElement.innerHTML = `
                    <strong>Nom :</strong> ${user.first_name} ${user.last_name}<br>
                    <strong>Email :</strong> ${user.email}<br>
                    <strong>Téléphone :</strong> ${user.phone || 'Non renseigné'}<br>
                    <strong>Adresse :</strong> ${user.address || 'Non renseignée'}
                `;
                profileTests.style.display = 'block';
                passwordTests.style.display = 'block';
                updateChecklist('check-login', true);
                log(`Utilisateur connecté: ${user.email}`);
            } else {
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ Utilisateur non connecté';
                userInfoElement.innerHTML = '<em>Connectez-vous pour tester les fonctionnalités</em>';
                profileTests.style.display = 'none';
                passwordTests.style.display = 'none';
                updateChecklist('check-login', false);
                log('Aucun utilisateur connecté');
            }
        }

        function loginAsAdmin() {
            log('Connexion comme Admin...');
            const token = btoa('<EMAIL>:admin:' + Date.now());
            const userInfo = {
                user_id: 1,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                phone: '+1234567890',
                address: '123 Admin Street, Admin City',
                created_at: '2023-01-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                log('Connexion Admin réussie');
                setTimeout(updateStatus, 100);
            }
        }

        function loginAsCustomer() {
            log('Connexion comme Client...');
            const token = btoa('<EMAIL>:customer:' + Date.now());
            const userInfo = {
                user_id: 2,
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                role: 'customer',
                phone: '',
                address: '',
                created_at: '2023-06-01T00:00:00Z'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                log('Connexion Client réussie');
                setTimeout(updateStatus, 100);
            }
        }

        function logout() {
            log('Déconnexion...');
            if (window.authManager) {
                window.authManager.logout();
                log('Déconnexion réussie');
                setTimeout(updateStatus, 100);
            }
        }

        function testEditProfile() {
            log('Test du formulaire Edit Profile...');
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.editProfile();
                document.getElementById('edit-profile-status').style.display = 'block';
                updateChecklist('check-edit-form', true);
                log('Formulaire Edit Profile ouvert');
            } else {
                log('❌ Utilisateur non connecté');
            }
        }

        function testChangePassword() {
            log('Test du formulaire Change Password...');
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.changePassword();
                document.getElementById('change-password-status').style.display = 'block';
                updateChecklist('check-password-form', true);
                log('Formulaire Change Password ouvert');
            } else {
                log('❌ Utilisateur non connecté');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Page de test Edit Profile chargée');
            setTimeout(updateStatus, 500);
            
            // Listen for auth events
            window.addEventListener('userLoggedIn', (event) => {
                log(`Événement: Utilisateur connecté - ${event.detail.email}`);
                setTimeout(updateStatus, 100);
            });
            
            window.addEventListener('userLoggedOut', () => {
                log('Événement: Utilisateur déconnecté');
                setTimeout(updateStatus, 100);
            });
        });
    </script>
</body>
</html>
