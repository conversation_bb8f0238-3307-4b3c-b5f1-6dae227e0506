/* Products Page Styles */
.page-header {
    background-color: var(--gray-color);
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(90, 85, 185, 0.1) 0%, rgba(127, 120, 210, 0.1) 100%);
}

.page-header h1 {
    margin-bottom: 15px;
    font-size: 36px;
    position: relative;
    color: var(--dark-color);
}

.breadcrumb {
    font-size: 14px;
    color: #777;
    position: relative;
}

.breadcrumb a {
    color: var(--primary-color);
    transition: color var(--transition-speed);
}

.breadcrumb a:hover {
    color: var(--secondary-color);
}

.products-section {
    padding: 70px 0;
}

.products-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

/* Filters Sidebar */
.filters-sidebar {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
    align-self: start;
    position: sticky;
    top: 90px;
}

.filter-group {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 25px;
}

.filter-group:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 20px;
}

.filter-group h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--dark-color);
    font-weight: 600;
}

.filter-list {
    list-style: none;
    padding: 0;
}

.filter-list li {
    margin-bottom: 12px;
}

.filter-list label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 15px;
    transition: color var(--transition-speed);
}

.filter-list label:hover {
    color: var(--primary-color);
}

.filter-list input[type="checkbox"] {
    margin-right: 12px;
    cursor: pointer;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.price-range {
    margin-top: 20px;
}

.price-range input {
    width: 100%;
    margin-bottom: 15px;
    cursor: pointer;
    accent-color: var(--primary-color);
    height: 6px;
}

.price-values {
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    font-weight: 500;
}

#apply-filters, #clear-filters {
    width: 100%;
    margin-bottom: 15px;
    padding: 12px 20px;
    font-weight: 600;
}

/* Products Main Area */
.products-main {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 20px 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.products-count {
    font-size: 16px;
}

.products-count span {
    font-weight: bold;
    color: var(--primary-color);
}

.products-sort {
    display: flex;
    align-items: center;
}

.products-sort label {
    margin-right: 12px;
    font-weight: 500;
}

.products-sort select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 14px;
    cursor: pointer;
    transition: border-color var(--transition-speed);
    min-width: 180px;
}

.products-sort select:hover,
.products-sort select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* No Products Message */
.no-products {
    padding: 40px;
    text-align: center;
    background-color: white;
    border-radius: var(--border-radius);
    margin: 20px 0;
    font-size: 16px;
    color: #777;
}

/* Skeleton Loading */
.skeleton {
    position: relative;
    overflow: hidden;
}

.skeleton::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-img {
    height: 250px;
    background-color: #f5f5f5;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.skeleton-text {
    height: 16px;
    background-color: #eee;
    margin-bottom: 10px;
    border-radius: 4px;
}

.skeleton-title {
    width: 80%;
}

.skeleton-price {
    width: 50%;
}

.skeleton-rating {
    width: 70%;
}

.skeleton-btn {
    height: 40px;
    background-color: #eee;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
}

.pagination-btn {
    background-color: white;
    border: 1px solid #ddd;
    width: 45px;
    height: 45px;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.pagination-numbers {
    display: flex;
    margin: 0 12px;
}

.pagination-number {
    width: 45px;
    height: 45px;
    border: 1px solid #ddd;
    background-color: white;
    border-radius: var(--border-radius);
    margin: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-speed);
    font-weight: 500;
}

.pagination-number.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-number:hover:not(.active) {
    background-color: var(--gray-color);
}

/* Responsive design */
@media (max-width: 992px) {
    .products-container {
        grid-template-columns: 1fr;
    }
    
    .filters-sidebar {
        position: static;
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .products-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .pagination-numbers {
        display: none;
    }
    
    .pagination {
        justify-content: space-between;
        width: 100%;
    }
    
    .page-header {
        padding: 40px 0;
    }
    
    .page-header h1 {
        font-size: 28px;
    }
} 