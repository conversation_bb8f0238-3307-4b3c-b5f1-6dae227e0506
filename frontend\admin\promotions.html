<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Promotions</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li><a href="admin-dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li class="active"><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Promotions Management</h1>
                <div class="admin-user">
                    <span>Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="content-actions">
                <div class="search-box">
                    <input type="text" id="promotion-search" placeholder="Search promotions...">
                    <button><i class="fas fa-search"></i></button>
                </div>
                <button class="btn primary-btn" id="add-promotion-btn">
                    <i class="fas fa-plus"></i> Add Promotion
                </button>
            </div>
            
            <div class="data-table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Discount</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="promotions-table-body">
                        <!-- Promotions will be loaded here -->
                    </tbody>
                </table>
                
                <div class="pagination">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button class="pagination-btn" id="next-page" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
        
        <!-- Add/Edit Promotion Modal -->
        <div class="modal" id="promotion-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">Add New Promotion</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="promotion-form">
                        <input type="hidden" id="promotion-id">
                        
                        <div class="form-group">
                            <label for="promotion-name">Promotion Name</label>
                            <input type="text" id="promotion-name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="promotion-description">Description</label>
                            <textarea id="promotion-description" name="description" rows="4"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="discount-percentage">Discount Percentage</label>
                            <input type="number" id="discount-percentage" name="discount_percentage" min="0" max="100" step="0.01" required>
                        </div>
                        
                        <div class="form-group date-group">
                            <div class="date-field">
                                <label for="start-date">Start Date</label>
                                <input type="datetime-local" id="start-date" name="start_date" required>
                            </div>
                            <div class="date-field">
                                <label for="end-date">End Date</label>
                                <input type="datetime-local" id="end-date" name="end_date" required>
                            </div>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <input type="checkbox" id="is-active" name="is_active">
                            <label for="is-active">Active</label>
                        </div>
                        
                        <div class="form-group">
                            <label>Applicable Products</label>
                            <div class="product-selector">
                                <div class="select-all-group">
                                    <input type="checkbox" id="select-all-products">
                                    <label for="select-all-products">Select All Products</label>
                                </div>
                                <div class="product-list" id="product-checkboxes">
                                    <!-- Product checkboxes will be loaded here -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn secondary-btn" id="cancel-btn">Cancel</button>
                            <button type="submit" class="btn primary-btn">Save Promotion</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div class="modal" id="delete-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Deletion</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this promotion? This action cannot be undone.</p>
                    
                    <div class="form-actions">
                        <button type="button" class="btn secondary-btn" id="cancel-delete-btn">Cancel</button>
                        <button type="button" class="btn danger-btn" id="confirm-delete-btn">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
    <script src="js/promotions.js"></script>
</body>
</html> 