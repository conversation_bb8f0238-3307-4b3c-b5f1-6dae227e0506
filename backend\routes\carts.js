const express = require('express');
const router = express.Router();
const { query } = require('../config/db');
const { authenticateJWT } = require('../middleware/auth');

/**
 * @route   GET /api/carts
 * @desc    Get current user's cart
 * @access  Private
 */
router.get('/', authenticateJWT, async (req, res) => {
  try {
    // Check if user has a cart
    let cartResult = await query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    // If no cart exists, create one
    if (cartResult.rows.length === 0) {
      cartResult = await query(
        'INSERT INTO shopping_carts (user_id) VALUES ($1) RETURNING *',
        [req.user.user_id]
      );
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Get cart items with product details
    const cartItemsResult = await query(`
      SELECT ci.cart_item_id, ci.quantity, ci.added_at,
             p.product_id, p.name, p.price, p.stock_quantity,
             b.name as brand_name,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.product_id
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      WHERE ci.cart_id = $1
      ORDER BY ci.added_at DESC
    `, [cartId]);
    
    // Calculate total
    let total = 0;
    cartItemsResult.rows.forEach(item => {
      total += parseFloat(item.price) * item.quantity;
    });
    
    res.json({
      success: true,
      cart: {
        cart_id: cartId,
        items: cartItemsResult.rows,
        item_count: cartItemsResult.rows.length,
        total: parseFloat(total.toFixed(2))
      }
    });
  } catch (error) {
    console.error('Get cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching cart'
    });
  }
});

/**
 * @route   POST /api/carts/items
 * @desc    Add item to cart
 * @access  Private
 */
router.post('/items', authenticateJWT, async (req, res) => {
  try {
    const { product_id, quantity = 1 } = req.body;
    
    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }
    
    // Check if product exists and has enough stock
    const productResult = await query(
      'SELECT * FROM products WHERE product_id = $1',
      [product_id]
    );
    
    if (productResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    const product = productResult.rows[0];
    
    if (product.stock_quantity < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Not enough stock available'
      });
    }
    
    // Get or create user's cart
    let cartResult = await query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    if (cartResult.rows.length === 0) {
      cartResult = await query(
        'INSERT INTO shopping_carts (user_id) VALUES ($1) RETURNING *',
        [req.user.user_id]
      );
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Check if item already exists in cart
    const existingItemResult = await query(
      'SELECT * FROM cart_items WHERE cart_id = $1 AND product_id = $2',
      [cartId, product_id]
    );
    
    if (existingItemResult.rows.length > 0) {
      // Update quantity
      const existingItem = existingItemResult.rows[0];
      const newQuantity = existingItem.quantity + quantity;
      
      if (newQuantity > product.stock_quantity) {
        return res.status(400).json({
          success: false,
          message: 'Not enough stock available'
        });
      }
      
      await query(
        'UPDATE cart_items SET quantity = $1, added_at = NOW() WHERE cart_item_id = $2',
        [newQuantity, existingItem.cart_item_id]
      );
    } else {
      // Add new item
      await query(
        'INSERT INTO cart_items (cart_id, product_id, quantity) VALUES ($1, $2, $3)',
        [cartId, product_id, quantity]
      );
    }
    
    // Get updated cart
    const updatedCartItemsResult = await query(`
      SELECT ci.cart_item_id, ci.quantity, ci.added_at,
             p.product_id, p.name, p.price, p.stock_quantity,
             b.name as brand_name,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.product_id
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      WHERE ci.cart_id = $1
      ORDER BY ci.added_at DESC
    `, [cartId]);
    
    // Calculate total
    let total = 0;
    updatedCartItemsResult.rows.forEach(item => {
      total += parseFloat(item.price) * item.quantity;
    });
    
    res.json({
      success: true,
      cart: {
        cart_id: cartId,
        items: updatedCartItemsResult.rows,
        item_count: updatedCartItemsResult.rows.length,
        total: parseFloat(total.toFixed(2))
      }
    });
  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while adding item to cart'
    });
  }
});

/**
 * @route   PUT /api/carts/items/:id
 * @desc    Update cart item
 * @access  Private
 */
router.put('/items/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity } = req.body;
    
    if (!quantity || quantity < 1) {
      return res.status(400).json({
        success: false,
        message: 'Quantity must be at least 1'
      });
    }
    
    // Get user's cart
    const cartResult = await query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    if (cartResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Check if cart item exists and belongs to user
    const cartItemResult = await query(
      'SELECT ci.*, p.stock_quantity FROM cart_items ci JOIN products p ON ci.product_id = p.product_id WHERE ci.cart_item_id = $1 AND ci.cart_id = $2',
      [id, cartId]
    );
    
    if (cartItemResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }
    
    const cartItem = cartItemResult.rows[0];
    
    // Check if requested quantity is available
    if (quantity > cartItem.stock_quantity) {
      return res.status(400).json({
        success: false,
        message: 'Not enough stock available'
      });
    }
    
    // Update cart item
    await query(
      'UPDATE cart_items SET quantity = $1, added_at = NOW() WHERE cart_item_id = $2',
      [quantity, id]
    );
    
    // Get updated cart
    const updatedCartItemsResult = await query(`
      SELECT ci.cart_item_id, ci.quantity, ci.added_at,
             p.product_id, p.name, p.price, p.stock_quantity,
             b.name as brand_name,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.product_id
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      WHERE ci.cart_id = $1
      ORDER BY ci.added_at DESC
    `, [cartId]);
    
    // Calculate total
    let total = 0;
    updatedCartItemsResult.rows.forEach(item => {
      total += parseFloat(item.price) * item.quantity;
    });
    
    res.json({
      success: true,
      cart: {
        cart_id: cartId,
        items: updatedCartItemsResult.rows,
        item_count: updatedCartItemsResult.rows.length,
        total: parseFloat(total.toFixed(2))
      }
    });
  } catch (error) {
    console.error('Update cart item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating cart item'
    });
  }
});

/**
 * @route   DELETE /api/carts/items/:id
 * @desc    Remove item from cart
 * @access  Private
 */
router.delete('/items/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get user's cart
    const cartResult = await query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    if (cartResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Check if cart item exists and belongs to user
    const cartItemResult = await query(
      'SELECT * FROM cart_items WHERE cart_item_id = $1 AND cart_id = $2',
      [id, cartId]
    );
    
    if (cartItemResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart item not found'
      });
    }
    
    // Delete cart item
    await query(
      'DELETE FROM cart_items WHERE cart_item_id = $1',
      [id]
    );
    
    // Get updated cart
    const updatedCartItemsResult = await query(`
      SELECT ci.cart_item_id, ci.quantity, ci.added_at,
             p.product_id, p.name, p.price, p.stock_quantity,
             b.name as brand_name,
             (SELECT image_url FROM product_images WHERE product_id = p.product_id AND is_primary = true LIMIT 1) as primary_image
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.product_id
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      WHERE ci.cart_id = $1
      ORDER BY ci.added_at DESC
    `, [cartId]);
    
    // Calculate total
    let total = 0;
    updatedCartItemsResult.rows.forEach(item => {
      total += parseFloat(item.price) * item.quantity;
    });
    
    res.json({
      success: true,
      cart: {
        cart_id: cartId,
        items: updatedCartItemsResult.rows,
        item_count: updatedCartItemsResult.rows.length,
        total: parseFloat(total.toFixed(2))
      },
      message: 'Item removed from cart'
    });
  } catch (error) {
    console.error('Remove cart item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while removing cart item'
    });
  }
});

/**
 * @route   DELETE /api/carts
 * @desc    Clear cart
 * @access  Private
 */
router.delete('/', authenticateJWT, async (req, res) => {
  try {
    // Get user's cart
    const cartResult = await query(
      'SELECT * FROM shopping_carts WHERE user_id = $1',
      [req.user.user_id]
    );
    
    if (cartResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    const cartId = cartResult.rows[0].cart_id;
    
    // Delete all cart items
    await query(
      'DELETE FROM cart_items WHERE cart_id = $1',
      [cartId]
    );
    
    res.json({
      success: true,
      cart: {
        cart_id: cartId,
        items: [],
        item_count: 0,
        total: 0
      },
      message: 'Cart cleared'
    });
  } catch (error) {
    console.error('Clear cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while clearing cart'
    });
  }
});

module.exports = router; 