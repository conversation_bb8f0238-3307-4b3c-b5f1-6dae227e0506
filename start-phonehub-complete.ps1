# PhoneHub E-Commerce - Système Complet avec Authentification
Write-Host "🚀 Démarrage de PhoneHub E-Commerce avec Authentification..." -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js requis. Installez Node.js d'abord." -ForegroundColor Red
    exit 1
}

# Check PostgreSQL
try {
    $pgService = Get-Service postgresql* | Where-Object {$_.Status -eq "Running"}
    if ($pgService) {
        Write-Host "✅ PostgreSQL: En cours d'exécution" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL: Non démarré" -ForegroundColor Red
        Write-Host "Démarrage de PostgreSQL..." -ForegroundColor Yellow
        Start-Service postgresql*
        Start-Sleep -Seconds 3
    }
} catch {
    Write-Host "❌ PostgreSQL non trouvé. Installez PostgreSQL." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Vérification des dépendances..." -ForegroundColor Yellow
Set-Location backend
if (!(Test-Path "node_modules")) {
    Write-Host "Installation des dépendances backend..." -ForegroundColor Yellow
    npm install
}
Set-Location ..

# Setup database
Write-Host "🗄️ Configuration de la base de données..." -ForegroundColor Yellow
node setup-db.js

Write-Host ""
Write-Host "🔧 Démarrage des serveurs..." -ForegroundColor Cyan

# Start backend
Write-Host "🔧 Démarrage du serveur backend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "
    Write-Host '🔧 BACKEND SERVER - PhoneHub API' -ForegroundColor Cyan
    Write-Host 'Port: 3000' -ForegroundColor White
    Write-Host 'API: http://localhost:3000/api' -ForegroundColor White
    Write-Host 'Base de données: PostgreSQL' -ForegroundColor White
    Write-Host 'Authentification: JWT + Mock bcrypt' -ForegroundColor White
    Write-Host ''
    cd '$PWD\backend'
    node server.js
"

Start-Sleep -Seconds 3

# Start frontend
Write-Host "🌐 Démarrage du serveur frontend..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "
    Write-Host '🌐 FRONTEND SERVER - PhoneHub Website' -ForegroundColor Cyan
    Write-Host 'Port: 8080' -ForegroundColor White
    Write-Host 'Site: http://localhost:8080' -ForegroundColor White
    Write-Host 'Admin: http://localhost:8080/admin' -ForegroundColor White
    Write-Host 'Authentification: Obligatoire pour panier' -ForegroundColor White
    Write-Host ''
    node '$PWD\frontend-server.js'
"

Start-Sleep -Seconds 5

# Test servers
Write-Host "🧪 Test des serveurs..." -ForegroundColor Yellow

try {
    $backendTest = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -UseBasicParsing -TimeoutSec 5
    if ($backendTest.StatusCode -eq 200) {
        Write-Host "✅ Backend API: Opérationnel" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Backend API: Non accessible" -ForegroundColor Red
}

try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
    if ($frontendTest.StatusCode -eq 200) {
        Write-Host "✅ Frontend: Opérationnel" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend: Non accessible" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 PhoneHub E-Commerce - SYSTÈME COMPLET DÉMARRÉ !" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host ""
Write-Host "📱 ACCÈS AU SITE :" -ForegroundColor Cyan
Write-Host "   🌐 Site Principal: http://localhost:8080" -ForegroundColor White
Write-Host "   🔧 Panel Admin: http://localhost:8080/admin" -ForegroundColor White
Write-Host "   🧪 Test Auth: http://localhost:8080/test-auth-system.html" -ForegroundColor White
Write-Host "   🔌 API: http://localhost:3000/api" -ForegroundColor White

Write-Host ""
Write-Host "👤 COMPTES DE CONNEXION :" -ForegroundColor Cyan
Write-Host "   📧 Admin: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Mot de passe: Admin@123" -ForegroundColor White
Write-Host "   📝 Clients: Inscription libre via le site" -ForegroundColor White

Write-Host ""
Write-Host "🔐 NOUVELLES FONCTIONNALITÉS :" -ForegroundColor Cyan
Write-Host "   ✅ Authentification obligatoire pour le panier" -ForegroundColor Green
Write-Host "   ✅ Panier spécifique à chaque utilisateur" -ForegroundColor Green
Write-Host "   ✅ Protection de la page panier" -ForegroundColor Green
Write-Host "   ✅ Vérification auth pour le checkout" -ForegroundColor Green
Write-Host "   ✅ Interface utilisateur dynamique" -ForegroundColor Green
Write-Host "   ✅ Modal d'authentification automatique" -ForegroundColor Green
Write-Host "   ✅ Actions en attente après connexion" -ForegroundColor Green

Write-Host ""
Write-Host "🛒 WORKFLOW UTILISATEUR :" -ForegroundColor Cyan
Write-Host "   1. Visiteur navigue sur le site" -ForegroundColor White
Write-Host "   2. Clic 'Add to Cart' → Modal de connexion" -ForegroundColor White
Write-Host "   3. Login/Register → Ajout automatique au panier" -ForegroundColor White
Write-Host "   4. Panier accessible uniquement si connecté" -ForegroundColor White
Write-Host "   5. Checkout avec vérification d'authentification" -ForegroundColor White

Write-Host ""
Write-Host "🛑 ARRÊT :" -ForegroundColor Yellow
Write-Host "   Fermez les fenêtres PowerShell ou Ctrl+C dans chaque terminal" -ForegroundColor White

Write-Host ""
Write-Host "🌐 Ouverture du site dans le navigateur..." -ForegroundColor Yellow
Start-Process "http://localhost:8080"

Write-Host ""
Write-Host "📊 Système prêt ! Bon développement ! 🚀" -ForegroundColor Green
