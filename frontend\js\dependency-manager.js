// ===== DEPENDENCY MANAGER - ROBUST INITIALIZATION SYSTEM =====
console.log('🔧 Loading Dependency Manager...');

// Global dependency manager
window.DependencyManager = {
    dependencies: new Map(),
    ready: new Set(),
    callbacks: new Map(),
    
    // Register a dependency
    register(name, checkFn) {
        this.dependencies.set(name, checkFn);
        console.log(`📦 Registered dependency: ${name}`);
    },
    
    // Mark a dependency as ready
    markReady(name) {
        if (this.dependencies.has(name)) {
            this.ready.add(name);
            console.log(`✅ Dependency ready: ${name}`);
            this.checkCallbacks();
        }
    },
    
    // Wait for dependencies
    waitFor(dependencies, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const check = () => {
                const allReady = dependencies.every(dep => this.isReady(dep));
                
                if (allReady) {
                    console.log(`✅ All dependencies ready: ${dependencies.join(', ')}`);
                    resolve();
                } else if (Date.now() - startTime > timeout) {
                    const missing = dependencies.filter(dep => !this.isReady(dep));
                    console.error(`❌ Timeout waiting for dependencies: ${missing.join(', ')}`);
                    reject(new Error(`Timeout waiting for dependencies: ${missing.join(', ')}`));
                } else {
                    setTimeout(check, 100);
                }
            };
            
            check();
        });
    },
    
    // Check if dependency is ready
    isReady(name) {
        if (this.ready.has(name)) return true;
        
        const checkFn = this.dependencies.get(name);
        if (checkFn && checkFn()) {
            this.markReady(name);
            return true;
        }
        
        return false;
    },
    
    // Execute callback when dependencies are ready
    onReady(dependencies, callback) {
        const callbackId = Date.now() + Math.random();
        this.callbacks.set(callbackId, { dependencies, callback });
        this.checkCallbacks();
        return callbackId;
    },
    
    // Check and execute ready callbacks
    checkCallbacks() {
        for (const [id, { dependencies, callback }] of this.callbacks) {
            const allReady = dependencies.every(dep => this.isReady(dep));
            if (allReady) {
                try {
                    callback();
                    this.callbacks.delete(id);
                } catch (error) {
                    console.error('Error executing callback:', error);
                }
            }
        }
    },
    
    // Get status of all dependencies
    getStatus() {
        const status = {};
        for (const [name] of this.dependencies) {
            status[name] = this.isReady(name);
        }
        return status;
    }
};

// Register core dependencies
window.DependencyManager.register('DOM', () => {
    return document.readyState === 'complete' || document.readyState === 'interactive';
});

window.DependencyManager.register('RaoufStoreData', () => {
    return window.RaoufStoreData && window.RaoufStoreData.initialized;
});

window.DependencyManager.register('AuthManager', () => {
    return window.authManager && typeof window.authManager.isAuthenticated === 'function';
});

window.DependencyManager.register('DataManager', () => {
    return window.RaoufStoreData && 
           typeof window.getAllProducts === 'function' &&
           typeof window.getAllUsers === 'function';
});

window.DependencyManager.register('CreateProductCard', () => {
    return typeof window.createProductCard === 'function';
});

// Auto-check DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.DependencyManager.markReady('DOM');
    });
} else {
    window.DependencyManager.markReady('DOM');
}

// Utility functions for safe async operations
window.safeAsyncCall = async function(fn, fallback = null, errorMessage = 'Async operation failed') {
    try {
        return await fn();
    } catch (error) {
        console.error(errorMessage, error);
        return fallback;
    }
};

window.safeCall = function(fn, fallback = null, errorMessage = 'Function call failed') {
    try {
        return fn();
    } catch (error) {
        console.error(errorMessage, error);
        return fallback;
    }
};

// Enhanced error handling for common operations
window.safeLocalStorageGet = function(key, defaultValue = null) {
    try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
    } catch (error) {
        console.error(`Error reading from localStorage (${key}):`, error);
        return defaultValue;
    }
};

window.safeLocalStorageSet = function(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error(`Error writing to localStorage (${key}):`, error);
        return false;
    }
};

// Debug helper
window.debugDependencies = function() {
    console.log('🔍 Dependency Status:', window.DependencyManager.getStatus());
    console.log('📦 Registered Dependencies:', Array.from(window.DependencyManager.dependencies.keys()));
    console.log('✅ Ready Dependencies:', Array.from(window.DependencyManager.ready));
    console.log('⏳ Pending Callbacks:', window.DependencyManager.callbacks.size);
};

// Global error handler
window.addEventListener('error', (event) => {
    console.error('🚨 Global Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 Unhandled Promise Rejection:', event.reason);
    event.preventDefault(); // Prevent default browser behavior
});

console.log('✅ Dependency Manager loaded successfully');

// Export for modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.DependencyManager;
}
