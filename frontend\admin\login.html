<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Login</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .login-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: #f44336;
            padding: 10px;
            border-radius: var(--border-radius);
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        
        .login-box.error {
            animation: shake 0.5s;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="login-page">
    <div class="admin-login-container">
        <div class="login-box">
            <div class="login-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            
            <form id="admin-login-form">
                <div class="form-group">
                    <label for="admin-email">Email</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="admin-email" name="email" autocomplete="username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="admin-password">Password</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="admin-password" name="password" autocomplete="current-password" required>
                    </div>
                </div>
                
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="remember-admin" name="remember">
                    <label for="remember-admin">Remember me</label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="primary-btn">Login</button>
                </div>
                
                <div class="form-footer">
                    <a href="../index.html">Return to Main Site</a>
                </div>
            </form>
        </div>
    </div>

    <script src="js/admin-app.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
</body>
</html> 