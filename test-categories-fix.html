<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Categories Fix - RaoufStore</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .console-output { background: #000; color: #00ff00; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; max-height: 300px; overflow-y: auto; }
        .data-display { background: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd; margin: 10px 0; }
        .filter-preview { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0; }
        .filter-group { background: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd; }
        .filter-group h4 { margin-top: 0; color: #007bff; }
        .filter-item { padding: 5px 0; border-bottom: 1px solid #eee; }
        .filter-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <h1>🔧 Test Categories Fix - RaoufStore</h1>
    
    <div class="test-card">
        <h2>🎯 Objectif</h2>
        <p>Corriger l'erreur "Failed to load categories" en s'assurant que les catégories et marques se chargent correctement depuis le data manager.</p>
    </div>
    
    <div class="test-card">
        <h2>🔧 Actions de Test</h2>
        
        <button class="btn" onclick="checkDataManager()">
            📊 Vérifier Data Manager
        </button>
        
        <button class="btn" onclick="initializeData()">
            🔄 Initialiser Données
        </button>
        
        <button class="btn" onclick="testCategoriesLoad()">
            📱 Tester Chargement Catégories
        </button>
        
        <button class="btn success" onclick="goToProducts()">
            🛒 Aller aux Produits
        </button>
        
        <button class="btn danger" onclick="clearConsole()">
            🗑️ Vider Console
        </button>
    </div>
    
    <div class="test-card">
        <h2>📊 Statut Data Manager</h2>
        <div id="data-status" class="status info">
            Cliquez sur "Vérifier Data Manager" pour voir le statut.
        </div>
    </div>
    
    <div class="test-card">
        <h2>📱 Aperçu des Filtres</h2>
        <div class="filter-preview">
            <div class="filter-group">
                <h4>📂 Catégories</h4>
                <div id="categories-preview">
                    Aucune catégorie chargée
                </div>
            </div>
            <div class="filter-group">
                <h4>🏷️ Marques</h4>
                <div id="brands-preview">
                    Aucune marque chargée
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-card">
        <h2>🖥️ Console Output</h2>
        <div id="console-output" class="console-output">
            Console logs apparaîtront ici...
        </div>
    </div>
    
    <div class="test-card">
        <h2>📋 Instructions de Test</h2>
        <ol>
            <li><strong>Vérifier Data Manager</strong> - Confirme que le data manager est chargé</li>
            <li><strong>Initialiser Données</strong> - Force l'initialisation des catégories et marques</li>
            <li><strong>Tester Chargement</strong> - Simule le chargement des catégories comme dans products.js</li>
            <li><strong>Aller aux Produits</strong> - Test final sur la vraie page produits</li>
        </ol>
    </div>

    <script src="frontend/js/data-manager.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...<br>';
        }
        
        function checkDataManager() {
            log('🔍 Checking Data Manager status...');
            
            if (window.RaoufStoreData) {
                log('✅ RaoufStoreData exists');
                log(`📊 Initialized: ${window.RaoufStoreData.initialized}`);
                log(`👥 Users: ${window.RaoufStoreData.users.length}`);
                log(`🛒 Orders: ${window.RaoufStoreData.orders.length}`);
                log(`📱 Products: ${window.RaoufStoreData.products.length}`);
                log(`📂 Categories: ${window.RaoufStoreData.categories.length}`);
                log(`🏷️ Brands: ${window.RaoufStoreData.brands.length}`);
                
                updateDataStatus();
                updateFilterPreviews();
            } else {
                log('❌ RaoufStoreData not found');
                document.getElementById('data-status').className = 'status error';
                document.getElementById('data-status').innerHTML = '<strong>❌ RaoufStoreData not found</strong>';
            }
        }
        
        function updateDataStatus() {
            const statusDiv = document.getElementById('data-status');
            
            if (window.RaoufStoreData.categories.length > 0 && window.RaoufStoreData.brands.length > 0) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    <strong>✅ Data Manager OK !</strong><br>
                    Categories: ${window.RaoufStoreData.categories.length} | 
                    Brands: ${window.RaoufStoreData.brands.length} | 
                    Products: ${window.RaoufStoreData.products.length} | 
                    Initialized: ${window.RaoufStoreData.initialized}
                `;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <strong>❌ Données manquantes !</strong><br>
                    Categories: ${window.RaoufStoreData.categories.length} | 
                    Brands: ${window.RaoufStoreData.brands.length}
                `;
            }
        }
        
        function updateFilterPreviews() {
            // Update categories preview
            const categoriesDiv = document.getElementById('categories-preview');
            if (window.RaoufStoreData.categories.length > 0) {
                categoriesDiv.innerHTML = window.RaoufStoreData.categories.map(cat => 
                    `<div class="filter-item">${cat.id}. ${cat.name}</div>`
                ).join('');
            } else {
                categoriesDiv.innerHTML = 'Aucune catégorie trouvée';
            }
            
            // Update brands preview
            const brandsDiv = document.getElementById('brands-preview');
            if (window.RaoufStoreData.brands.length > 0) {
                brandsDiv.innerHTML = window.RaoufStoreData.brands.map(brand => 
                    `<div class="filter-item">${brand.id}. ${brand.name}</div>`
                ).join('');
            } else {
                brandsDiv.innerHTML = 'Aucune marque trouvée';
            }
        }
        
        function initializeData() {
            log('🔄 Force initializing data...');
            
            try {
                if (window.initializeDataManager) {
                    window.initializeDataManager();
                    log('✅ Data manager re-initialized');
                } else {
                    log('❌ initializeDataManager function not found');
                }
                
                setTimeout(() => {
                    checkDataManager();
                }, 1000);
                
            } catch (error) {
                log('❌ Error initializing data: ' + error.message);
            }
        }
        
        async function testCategoriesLoad() {
            log('🧪 Testing categories loading (simulating products.js)...');
            
            try {
                // Simulate the loadCategoriesAndBrands function from products.js
                let attempts = 0;
                while ((!window.RaoufStoreData || !window.RaoufStoreData.initialized) && attempts < 50) {
                    log(`⏳ Waiting for data manager... attempt ${attempts + 1}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (window.RaoufStoreData && window.RaoufStoreData.initialized) {
                    if (window.RaoufStoreData.categories && window.RaoufStoreData.categories.length > 0) {
                        log(`✅ Categories loaded successfully: ${window.RaoufStoreData.categories.length} items`);
                        window.RaoufStoreData.categories.forEach(cat => {
                            log(`  📂 ${cat.id}: ${cat.name}`);
                        });
                    } else {
                        log('⚠️ No categories found in data manager');
                    }
                    
                    if (window.RaoufStoreData.brands && window.RaoufStoreData.brands.length > 0) {
                        log(`✅ Brands loaded successfully: ${window.RaoufStoreData.brands.length} items`);
                        window.RaoufStoreData.brands.forEach(brand => {
                            log(`  🏷️ ${brand.id}: ${brand.name}`);
                        });
                    } else {
                        log('⚠️ No brands found in data manager');
                    }
                    
                    updateFilterPreviews();
                } else {
                    log('❌ Data manager not ready after waiting');
                }
                
            } catch (error) {
                log('❌ Error testing categories load: ' + error.message);
            }
        }
        
        function goToProducts() {
            log('📱 Redirecting to products page...');
            window.location.href = 'http://localhost:8080/frontend/products.html';
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Categories Fix test page loaded');
            setTimeout(() => {
                checkDataManager();
            }, 1000);
        });
    </script>
</body>
</html>
