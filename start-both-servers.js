// Script pour démarrer le frontend et backend sur des ports disponibles
const express = require('express');
const path = require('path');
const cors = require('cors');

// Configuration des ports
const FRONTEND_PORT = 8080;
const BACKEND_PORT = 3001; // Port différent pour éviter les conflits

console.log('🚀 Démarrage des serveurs RaoufStore...');

// ===== SERVEUR FRONTEND =====
const frontendApp = express();

// Middleware pour servir les fichiers statiques
frontendApp.use(express.static(path.join(__dirname, 'frontend')));
frontendApp.use('/frontend', express.static(path.join(__dirname, 'frontend')));

// Route pour la racine
frontendApp.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

// Routes pour les pages principales
frontendApp.get('/index.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

frontendApp.get('/products.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'products.html'));
});

frontendApp.get('/cart.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'cart.html'));
});

frontendApp.get('/login.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'login.html'));
});

// Démarrer le serveur frontend
frontendApp.listen(FRONTEND_PORT, () => {
    console.log(`✅ Serveur Frontend démarré sur http://localhost:${FRONTEND_PORT}`);
    console.log(`📱 Site principal: http://localhost:${FRONTEND_PORT}/frontend/index.html`);
    console.log(`🔧 Admin panel: http://localhost:${FRONTEND_PORT}/frontend/admin/index.html`);
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  Port ${FRONTEND_PORT} déjà utilisé - Le serveur frontend fonctionne déjà`);
        console.log(`📱 Site accessible sur: http://localhost:${FRONTEND_PORT}/frontend/index.html`);
    } else {
        console.error('❌ Erreur serveur frontend:', err);
    }
});

// ===== SERVEUR BACKEND =====
const backendApp = express();

// Middleware
backendApp.use(cors());
backendApp.use(express.json());
backendApp.use(express.urlencoded({ extended: true }));

// Log des requêtes
backendApp.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`);
    next();
});

// Routes API
backendApp.get('/api', (req, res) => {
    res.json({
        message: 'Welcome to RaoufStore E-Commerce API',
        status: 'Running',
        version: '1.0.0',
        endpoints: {
            health: '/api/health',
            products: '/api/products',
            users: '/api/users'
        }
    });
});

backendApp.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'RaoufStore API is running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Route produits simple
backendApp.get('/api/products', (req, res) => {
    res.json({
        success: true,
        products: [
            {
                id: 1,
                name: 'iPhone 13 Pro Max',
                price: 1099.99,
                description: 'Apple flagship smartphone',
                brand: 'Apple',
                category: 'Smartphones',
                stock: 50,
                image: 'images/real-products/iphone13_main.jpg'
            },
            {
                id: 2,
                name: 'Samsung Galaxy S21',
                price: 799.99,
                description: 'Samsung flagship smartphone',
                brand: 'Samsung',
                category: 'Smartphones',
                stock: 30,
                image: 'images/real-products/samsung_s21_main.jpg'
            },
            {
                id: 3,
                name: 'Google Pixel 6',
                price: 699.00,
                description: 'Google flagship smartphone',
                brand: 'Google',
                category: 'Smartphones',
                stock: 25,
                image: 'images/real-products/pixel6_main.jpg'
            }
        ]
    });
});

// Route utilisateurs simple
backendApp.get('/api/users', (req, res) => {
    res.json({
        success: true,
        message: 'Users endpoint - Authentication required',
        users: []
    });
});

// Gestion d'erreurs
backendApp.use((err, req, res, next) => {
    console.error('❌ Erreur API:', err);
    res.status(500).json({
        success: false,
        message: 'Erreur serveur',
        error: err.message
    });
});

// Démarrer le serveur backend
backendApp.listen(BACKEND_PORT, () => {
    console.log(`✅ Serveur Backend démarré sur http://localhost:${BACKEND_PORT}`);
    console.log(`🔗 API accessible sur: http://localhost:${BACKEND_PORT}/api`);
    console.log(`💚 Health check: http://localhost:${BACKEND_PORT}/api/health`);
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  Port ${BACKEND_PORT} déjà utilisé - Essai du port suivant...`);
        // Essayer le port suivant
        backendApp.listen(BACKEND_PORT + 1, () => {
            console.log(`✅ Serveur Backend démarré sur http://localhost:${BACKEND_PORT + 1}`);
            console.log(`🔗 API accessible sur: http://localhost:${BACKEND_PORT + 1}/api`);
        });
    } else {
        console.error('❌ Erreur serveur backend:', err);
    }
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt des serveurs...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt des serveurs...');
    process.exit(0);
});

console.log('\n📋 INFORMATIONS IMPORTANTES:');
console.log('🌐 Frontend (Site): http://localhost:8080/frontend/index.html');
console.log('🔧 Admin Panel: http://localhost:8080/frontend/admin/index.html');
console.log('🔗 Backend API: http://localhost:3001/api');
console.log('💚 Health Check: http://localhost:3001/api/health');
console.log('\n👤 Comptes de test:');
console.log('📧 Admin: <EMAIL> / Admin@123');
console.log('📧 Client: <EMAIL> / User@123');
console.log('\n⏹️  Pour arrêter: Ctrl+C');
console.log('🔄 Les serveurs redémarrent automatiquement en cas d\'erreur');
