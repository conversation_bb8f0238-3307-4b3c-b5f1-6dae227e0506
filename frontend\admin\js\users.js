document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const usersTableBody = document.getElementById('users-table-body');
    const userSearch = document.getElementById('user-search');
    const roleFilter = document.getElementById('role-filter');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageInfo = document.getElementById('page-info');
    const userModal = document.getElementById('user-modal');
    const closeBtn = userModal.querySelector('.close-btn');

    // State
    let users = [];
    let filteredUsers = [];
    let currentPage = 1;
    let itemsPerPage = 10;
    
    // Initialize
    init();
    
    // Functions
    function init() {
        loadUsers();
        
        // Event listeners
        userSearch.addEventListener('input', handleSearch);
        roleFilter.addEventListener('change', handleRoleFilter);
        prevPageBtn.addEventListener('click', () => changePage(currentPage - 1));
        nextPageBtn.addEventListener('click', () => changePage(currentPage + 1));
        closeBtn.addEventListener('click', closeModal);
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === userModal) {
                closeModal();
            }
        });
    }
    
    function loadUsers() {
        console.log('👥 Loading users from data manager...');

        // Get users from global data manager
        if (window.RaoufStoreData && window.RaoufStoreData.users) {
            users = [...window.RaoufStoreData.users];
            console.log('👥 Loaded users from data manager:', users.length);
        } else if (window.getAllUsers) {
            users = window.getAllUsers();
            console.log('👥 Loaded users from function:', users.length);
        } else {
            // Fallback to sample data
            users = getSampleUsers();
            console.log('👥 Using sample users:', users.length);
        }

        filteredUsers = [...users];
        renderUsers();
        updatePagination();
        console.log('✅ Users loaded successfully');
    }

    function getSampleUsers() {
        return [
            {
                id: 1,
                username: 'admin',
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                phone: '************',
                address: '123 Admin St, Admin City, AC 12345',
                role: 'admin',
                joined_date: '2023-01-01',
                orders_count: 0,
                reviews_count: 0,
                total_spent: 0,
                recent_orders: []
            },
            {
                id: 2,
                username: 'johndoe',
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                phone: '************',
                address: '456 Main St, Anytown, AT 67890',
                role: 'customer',
                joined_date: '2023-02-15',
                orders_count: 2,
                reviews_count: 1,
                total_spent: 949.98,
                recent_orders: []
            },
            {
                id: 3,
                username: 'janedoe',
                email: '<EMAIL>',
                first_name: 'Jane',
                last_name: 'Doe',
                phone: '************',
                address: '789 Oak St, Springfield, SP 54321',
                role: 'customer',
                joined_date: '2023-03-01',
                orders_count: 1,
                reviews_count: 0,
                total_spent: 899.99,
                recent_orders: []
            }
        ];
    }
    
    function renderUsers() {
        // Clear the table
        usersTableBody.innerHTML = '';
        
        // Calculate page slice
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const usersToShow = filteredUsers.slice(startIndex, endIndex);
        
        // Show message if no users found
        if (usersToShow.length === 0) {
            usersTableBody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center;">No users found</td>
                </tr>
            `;
            return;
        }
        
        // Render the users
        usersToShow.forEach(user => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${user.first_name} ${user.last_name}</td>
                <td>
                    <span class="status-badge ${user.role === 'admin' ? 'status-delivered' : 'status-processing'}">
                        ${user.role}
                    </span>
                </td>
                <td>${formatDate(user.joined_date)}</td>
                <td>
                    <button class="btn small-btn edit-btn" data-id="${user.id}">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn small-btn delete-btn" data-id="${user.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            // Add event listeners
            const viewBtn = tr.querySelector('.edit-btn');
            const deleteBtn = tr.querySelector('.delete-btn');
            
            viewBtn.addEventListener('click', () => showUserDetails(user.id));
            deleteBtn.addEventListener('click', () => deleteUser(user.id));
            
            usersTableBody.appendChild(tr);
        });
    }
    
    function updatePagination() {
        const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
        
        // Update page info
        pageInfo.textContent = `Page ${currentPage} of ${totalPages || 1}`;
        
        // Update buttons
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
    }
    
    function changePage(page) {
        currentPage = page;
        renderUsers();
        updatePagination();
    }
    
    function handleSearch() {
        const searchTerm = userSearch.value.toLowerCase();
        
        if (searchTerm.trim() === '') {
            filteredUsers = [...users];
        } else {
            filteredUsers = users.filter(user => 
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.first_name.toLowerCase().includes(searchTerm) ||
                user.last_name.toLowerCase().includes(searchTerm)
            );
        }
        
        currentPage = 1;
        renderUsers();
        updatePagination();
    }
    
    function handleRoleFilter() {
        const role = roleFilter.value;
        
        if (role === '') {
            filteredUsers = [...users];
        } else {
            filteredUsers = users.filter(user => user.role === role);
        }
        
        currentPage = 1;
        renderUsers();
        updatePagination();
    }
    
    function showUserDetails(userId) {
        const user = users.find(user => user.id === userId);
        
        if (!user) return;
        
        // Populate modal with user details
        document.getElementById('detail-username').textContent = user.username;
        document.getElementById('detail-email').textContent = user.email;
        document.getElementById('detail-first-name').textContent = user.first_name;
        document.getElementById('detail-last-name').textContent = user.last_name;
        document.getElementById('detail-phone').textContent = user.phone || 'N/A';
        document.getElementById('detail-address').textContent = user.address || 'N/A';
        document.getElementById('detail-role').textContent = user.role;
        document.getElementById('detail-joined').textContent = formatDate(user.joined_date);
        
        // Stats
        document.getElementById('orders-count').textContent = user.orders_count;
        document.getElementById('reviews-count').textContent = user.reviews_count;
        document.getElementById('total-spent').textContent = `$${user.total_spent.toFixed(2)}`;
        
        // Recent orders
        const ordersBody = document.getElementById('user-orders-body');
        ordersBody.innerHTML = '';
        
        if (user.recent_orders && user.recent_orders.length > 0) {
            user.recent_orders.forEach(order => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${order.id}</td>
                    <td>${formatDate(order.date)}</td>
                    <td>$${order.total.toFixed(2)}</td>
                    <td>
                        <span class="status-badge status-${order.status}">
                            ${order.status}
                        </span>
                    </td>
                `;
                ordersBody.appendChild(tr);
            });
        } else {
            ordersBody.innerHTML = `
                <tr>
                    <td colspan="4" style="text-align: center;">No orders found</td>
                </tr>
            `;
        }
        
        // Show modal
        userModal.style.display = 'flex';
    }
    
    function closeModal() {
        userModal.style.display = 'none';
    }
    
    function deleteUser(userId) {
        const user = users.find(u => u.id === userId);
        if (!user) {
            console.error('User not found:', userId);
            return;
        }

        // Prevent deletion of admin user
        if (user.role === 'admin') {
            alert('Cannot delete admin user!');
            return;
        }

        if (confirm(`Are you sure you want to delete user "${user.first_name} ${user.last_name}" (${user.email})?`)) {
            console.log('🗑️ Deleting user:', user.email);

            // Remove from local users array
            const index = users.findIndex(user => user.id === userId);
            if (index !== -1) {
                users.splice(index, 1);
            }

            // Remove from global data manager
            if (window.RaoufStoreData && window.RaoufStoreData.users) {
                const globalIndex = window.RaoufStoreData.users.findIndex(u => u.id === userId);
                if (globalIndex !== -1) {
                    window.RaoufStoreData.users.splice(globalIndex, 1);
                    console.log('🗑️ User removed from data manager');

                    // Save to storage
                    if (window.saveDataToStorage) {
                        window.saveDataToStorage();
                        console.log('💾 Data saved to storage');
                    }
                }
            }

            // Update filtered users and refresh display
            filteredUsers = [...users];
            renderUsers();
            updatePagination();

            // Show success notification
            showNotification(`User "${user.first_name} ${user.last_name}" deleted successfully!`);
            console.log('✅ User deleted successfully');
        }
    }
    
    function formatDate(dateString) {
        const options = { year: 'numeric', month: 'short', day: 'numeric' };
        return new Date(dateString).toLocaleDateString('en-US', options);
    }

    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            padding: 15px 20px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);

        console.log('🔔 Notification:', message);
    }
});