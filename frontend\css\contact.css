/* Contact Page Styles */

.contact-section {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 2rem;
}

.contact-info > p {
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

.info-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.info-content h3 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.info-content p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.social-links {
    margin-top: 40px;
}

.social-links h3 {
    color: #333;
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
}

.contact-form-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form-container h2 {
    color: #333;
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.map-section {
    padding: 60px 0;
    background: white;
}

.map-container {
    margin-top: 30px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.map-placeholder {
    height: 400px;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
}

.map-placeholder i {
    font-size: 3rem;
    margin-top: 10px;
    color: #667eea;
}

.faq-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.faq-question {
    padding: 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.faq-question h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.faq-toggle {
    color: #667eea;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 25px 25px;
    max-height: 200px;
}

.faq-answer p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .contact-form-container {
        padding: 30px 20px;
    }
    
    .info-item {
        margin-bottom: 25px;
    }
    
    .social-icons {
        justify-content: center;
    }
    
    .map-placeholder {
        height: 300px;
    }
    
    .faq-question {
        padding: 20px;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }
}
