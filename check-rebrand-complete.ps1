# Script pour vérifier que le rebrand PhoneHub → RaoufStore est complet
Write-Host "🔍 Vérification du rebrand PhoneHub → RaoufStore..." -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

$foundPhoneHub = $false
$foundOldEmails = $false

# Fichiers à vérifier
$filesToCheck = @(
    "frontend\index.html",
    "frontend\products.html", 
    "frontend\login.html",
    "frontend\cart.html",
    "frontend\about.html",
    "frontend\contact.html",
    "frontend\product-details.html",
    "frontend\admin\index.html",
    "frontend\admin\login.html",
    "frontend\admin\users.html",
    "frontend\admin\orders.html",
    "frontend\admin\products.html",
    "frontend\admin\brands.html",
    "frontend\admin\categories.html",
    "frontend\admin\promotions.html",
    "frontend\js\data-manager.js"
)

Write-Host "📁 Vérification des fichiers HTML et JS..." -ForegroundColor Yellow

foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        
        # Vérifier PhoneHub
        if ($content -match "PhoneHub") {
            Write-Host "❌ $file contient encore 'PhoneHub'" -ForegroundColor Red
            $foundPhoneHub = $true
        }
        
        # Vérifier anciens emails
        if ($content -match "phonehub\.com") {
            Write-Host "❌ $file contient encore 'phonehub.com'" -ForegroundColor Red
            $foundOldEmails = $true
        }
        
        # Vérifier si RaoufStore est présent
        if ($content -match "RaoufStore") {
            Write-Host "✅ $file contient 'RaoufStore'" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $file ne contient pas 'RaoufStore'" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  Fichier non trouvé: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📊 RÉSULTATS DE LA VÉRIFICATION :" -ForegroundColor Cyan

if (-not $foundPhoneHub -and -not $foundOldEmails) {
    Write-Host "🎉 REBRAND COMPLET !" -ForegroundColor Green
    Write-Host "✅ Aucune référence à 'PhoneHub' trouvée" -ForegroundColor Green
    Write-Host "✅ Aucune référence à 'phonehub.com' trouvée" -ForegroundColor Green
    Write-Host "✅ Le site utilise maintenant 'RaoufStore'" -ForegroundColor Green
} else {
    Write-Host "❌ REBRAND INCOMPLET" -ForegroundColor Red
    if ($foundPhoneHub) {
        Write-Host "❌ Des références à 'PhoneHub' existent encore" -ForegroundColor Red
    }
    if ($foundOldEmails) {
        Write-Host "❌ Des références à 'phonehub.com' existent encore" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🧪 TESTS RECOMMANDÉS :" -ForegroundColor Cyan
Write-Host "1. 🌐 Visitez: http://localhost:8080/frontend/index.html" -ForegroundColor White
Write-Host "2. 🔑 Connectez-vous admin: <EMAIL>" -ForegroundColor White
Write-Host "3. 🔧 Vérifiez admin: http://localhost:8080/frontend/admin/index.html" -ForegroundColor White
Write-Host "4. 🎨 Test rebrand: http://localhost:8080/test-raoufstore-rebrand.html" -ForegroundColor White

Write-Host ""
Write-Host "💾 VÉRIFICATION LOCALSTORAGE :" -ForegroundColor Cyan
Write-Host "Dans la console du navigateur, vérifiez :" -ForegroundColor White
Write-Host "console.log(window.RaoufStoreData); // Doit exister" -ForegroundColor Gray
Write-Host "console.log(window.PhoneHubData);   // Doit être undefined" -ForegroundColor Gray

Write-Host ""
Write-Host "🔧 Vérification terminée !" -ForegroundColor Green
