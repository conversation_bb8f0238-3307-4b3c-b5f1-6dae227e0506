// Authentication Management System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        this.checkAuthStatus();
        this.updateUI();
    }

    // Check if user is authenticated
    isAuthenticated() {
        const token = this.getToken();
        return token !== null && token !== undefined && token !== '';
    }

    // Get stored token
    getToken() {
        return localStorage.getItem('token') || sessionStorage.getItem('token');
    }

    // Get user role
    getUserRole() {
        return localStorage.getItem('userRole') || sessionStorage.getItem('userRole') || 'guest';
    }

    // Get current user info
    getCurrentUser() {
        if (!this.isAuthenticated()) return null;
        
        const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo');
        if (userInfo) {
            try {
                return JSON.parse(userInfo);
            } catch (e) {
                console.error('Error parsing user info:', e);
                return null;
            }
        }
        return null;
    }

    // Check authentication status
    checkAuthStatus() {
        if (this.isAuthenticated()) {
            this.currentUser = this.getCurrentUser();
        } else {
            this.currentUser = null;
        }
    }

    // Login user
    login(token, userInfo, remember = false) {
        const storage = remember ? localStorage : sessionStorage;
        
        storage.setItem('token', token);
        storage.setItem('userRole', userInfo.role || 'customer');
        storage.setItem('userInfo', JSON.stringify(userInfo));
        
        this.currentUser = userInfo;
        this.updateUI();
        
        // Trigger login event
        window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: userInfo }));
    }

    // Logout user
    logout() {
        // Clear all auth data
        localStorage.removeItem('token');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userInfo');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('userRole');
        sessionStorage.removeItem('userInfo');
        
        // Clear user-specific cart
        localStorage.removeItem('cart');
        
        this.currentUser = null;
        this.updateUI();
        
        // Trigger logout event
        window.dispatchEvent(new CustomEvent('userLoggedOut'));
        
        // Redirect to home page
        if (window.location.pathname !== '/index.html' && window.location.pathname !== '/') {
            window.location.href = 'index.html';
        }
    }

    // Update UI based on auth status
    updateUI() {
        const authButtons = document.querySelector('.auth-buttons');
        if (!authButtons) return;

        if (this.isAuthenticated()) {
            // User is logged in - show user menu
            authButtons.innerHTML = `
                <div class="user-menu">
                    <span class="user-greeting">Hello, ${this.currentUser?.first_name || 'User'}!</span>
                    <div class="user-dropdown">
                        <button class="user-menu-btn">
                            <i class="fas fa-user"></i>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-content">
                            <a href="#" id="user-profile"><i class="fas fa-user"></i> Profile</a>
                            <a href="#" id="user-orders"><i class="fas fa-shopping-bag"></i> My Orders</a>
                            ${this.getUserRole() === 'admin' ? '<a href="admin/index.html"><i class="fas fa-cog"></i> Admin Panel</a>' : ''}
                            <hr>
                            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            `;

            // Add event listeners
            document.getElementById('logout-btn')?.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });

            document.getElementById('user-profile')?.addEventListener('click', (e) => {
                e.preventDefault();
                this.showUserProfile();
            });

            document.getElementById('user-orders')?.addEventListener('click', (e) => {
                e.preventDefault();
                this.showUserOrders();
            });

            // Toggle dropdown
            const userMenuBtn = document.querySelector('.user-menu-btn');
            const dropdownContent = document.querySelector('.dropdown-content');
            
            userMenuBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                dropdownContent?.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                dropdownContent?.classList.remove('show');
            });

        } else {
            // User is not logged in - show login/register buttons
            authButtons.innerHTML = `
                <a href="login.html" class="login-btn">Login</a>
                <a href="login.html?tab=register" class="register-btn">Register</a>
            `;
        }
    }

    // Require authentication for action
    requireAuth(action, message = 'You need to login to perform this action.') {
        if (!this.isAuthenticated()) {
            this.showAuthModal(message, action);
            return false;
        }
        return true;
    }

    // Show authentication modal
    showAuthModal(message, redirectAction = null) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('auth-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'auth-modal';
            modal.className = 'auth-modal';
            modal.innerHTML = `
                <div class="auth-modal-content">
                    <div class="auth-modal-header">
                        <h3><i class="fas fa-lock"></i> Authentication Required</h3>
                        <button class="close-modal">&times;</button>
                    </div>
                    <div class="auth-modal-body">
                        <p id="auth-modal-message">${message}</p>
                        <div class="auth-modal-actions">
                            <a href="login.html" class="btn primary-btn">Login</a>
                            <a href="login.html?tab=register" class="btn secondary-btn">Register</a>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Close modal events
            modal.querySelector('.close-modal').addEventListener('click', () => {
                modal.style.display = 'none';
            });

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Update message and show modal
        document.getElementById('auth-modal-message').textContent = message;
        modal.style.display = 'flex';

        // Store redirect action if provided
        if (redirectAction) {
            sessionStorage.setItem('pendingAction', JSON.stringify(redirectAction));
        }
    }

    // Execute pending action after login
    executePendingAction() {
        const pendingAction = sessionStorage.getItem('pendingAction');
        if (pendingAction) {
            try {
                const action = JSON.parse(pendingAction);
                sessionStorage.removeItem('pendingAction');

                // Execute the pending action
                if (action.type === 'addToCart') {
                    // Re-trigger add to cart
                    window.dispatchEvent(new CustomEvent('executePendingAddToCart', { detail: action.data }));
                }
            } catch (e) {
                console.error('Error executing pending action:', e);
            }
        }
    }

    // Show user profile modal
    showUserProfile() {
        const user = this.getCurrentUser();
        if (!user) return;

        this.showModal('User Profile', `
            <div class="profile-info">
                <div class="profile-field">
                    <label><i class="fas fa-user"></i> Name:</label>
                    <span>${user.first_name} ${user.last_name}</span>
                </div>
                <div class="profile-field">
                    <label><i class="fas fa-envelope"></i> Email:</label>
                    <span>${user.email}</span>
                </div>
                <div class="profile-field">
                    <label><i class="fas fa-id-badge"></i> User ID:</label>
                    <span>${user.user_id}</span>
                </div>
                <div class="profile-field">
                    <label><i class="fas fa-shield-alt"></i> Role:</label>
                    <span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span>
                </div>
                <div class="profile-field">
                    <label><i class="fas fa-calendar"></i> Member Since:</label>
                    <span>${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</span>
                </div>
            </div>
            <div class="profile-actions">
                <button class="btn secondary-btn" onclick="window.authManager.editProfile()">
                    <i class="fas fa-edit"></i> Edit Profile
                </button>
                <button class="btn danger-btn" onclick="window.authManager.changePassword()">
                    <i class="fas fa-key"></i> Change Password
                </button>
            </div>
        `);
    }

    // Show user orders modal
    showUserOrders() {
        const user = this.getCurrentUser();
        if (!user) return;

        // Get user's cart history and simulate orders
        const userId = user.user_id;
        const cartKey = `cart_${userId}`;
        const orderHistoryKey = `orders_${userId}`;

        let orders = [];
        const savedOrders = localStorage.getItem(orderHistoryKey);
        if (savedOrders) {
            try {
                orders = JSON.parse(savedOrders);
            } catch (e) {
                console.error('Error parsing orders:', e);
            }
        }

        // If no orders, create sample orders for demo
        if (orders.length === 0) {
            orders = this.generateSampleOrders(userId);
            localStorage.setItem(orderHistoryKey, JSON.stringify(orders));
        }

        const ordersHtml = orders.length > 0 ? orders.map(order => `
            <div class="order-item">
                <div class="order-header">
                    <span class="order-id">#${order.id}</span>
                    <span class="order-date">${new Date(order.date).toLocaleDateString()}</span>
                    <span class="order-status status-${order.status}">${order.status}</span>
                </div>
                <div class="order-details">
                    <div class="order-items">
                        ${order.items.map(item => `
                            <div class="order-item-detail">
                                <span>${item.name}</span>
                                <span>Qty: ${item.quantity}</span>
                                <span>$${(item.price * item.quantity).toFixed(2)}</span>
                            </div>
                        `).join('')}
                    </div>
                    <div class="order-total">
                        <strong>Total: $${order.total.toFixed(2)}</strong>
                    </div>
                </div>
            </div>
        `).join('') : '<p class="no-orders">No orders found. Start shopping to see your orders here!</p>';

        this.showModal('My Orders', `
            <div class="orders-container">
                ${ordersHtml}
            </div>
            <div class="orders-actions">
                <button class="btn primary-btn" onclick="window.location.href='products.html'">
                    <i class="fas fa-shopping-cart"></i> Continue Shopping
                </button>
            </div>
        `);
    }

    // Generate sample orders for demo
    generateSampleOrders(userId) {
        const sampleProducts = [
            { name: 'iPhone 13 Pro Max', price: 1099.99 },
            { name: 'Samsung Galaxy S21', price: 799.99 },
            { name: 'Google Pixel 6', price: 699.00 },
            { name: 'OnePlus 9 Pro', price: 969.00 }
        ];

        const orders = [];
        const orderCount = Math.floor(Math.random() * 3) + 1; // 1-3 orders

        for (let i = 0; i < orderCount; i++) {
            const itemCount = Math.floor(Math.random() * 3) + 1; // 1-3 items per order
            const items = [];
            let total = 0;

            for (let j = 0; j < itemCount; j++) {
                const product = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];
                const quantity = Math.floor(Math.random() * 2) + 1; // 1-2 quantity
                const item = {
                    name: product.name,
                    price: product.price,
                    quantity: quantity
                };
                items.push(item);
                total += product.price * quantity;
            }

            const statuses = ['delivered', 'shipped', 'processing'];
            const order = {
                id: `ORD${Date.now()}${i}`,
                date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 30 days
                items: items,
                total: total,
                status: statuses[Math.floor(Math.random() * statuses.length)]
            };
            orders.push(order);
        }

        return orders.sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first
    }

    // Generic modal display method
    showModal(title, content) {
        // Remove existing modal if any
        const existingModal = document.getElementById('user-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal
        const modal = document.createElement('div');
        modal.id = 'user-modal';
        modal.className = 'user-modal';
        modal.innerHTML = `
            <div class="user-modal-content">
                <div class="user-modal-header">
                    <h3>${title}</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="user-modal-body">
                    ${content}
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Close modal events
        modal.querySelector('.close-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Show modal
        modal.style.display = 'flex';
    }

    // Edit profile
    editProfile() {
        const user = this.getCurrentUser();
        if (!user) return;

        this.showModal('Edit Profile', `
            <form id="edit-profile-form" class="profile-form">
                <div class="form-group">
                    <label for="edit-first-name"><i class="fas fa-user"></i> First Name:</label>
                    <input type="text" id="edit-first-name" name="firstName" value="${user.first_name}" required>
                </div>
                <div class="form-group">
                    <label for="edit-last-name"><i class="fas fa-user"></i> Last Name:</label>
                    <input type="text" id="edit-last-name" name="lastName" value="${user.last_name}" required>
                </div>
                <div class="form-group">
                    <label for="edit-email"><i class="fas fa-envelope"></i> Email:</label>
                    <input type="email" id="edit-email" name="email" value="${user.email}" required>
                </div>
                <div class="form-group">
                    <label for="edit-phone"><i class="fas fa-phone"></i> Phone:</label>
                    <input type="tel" id="edit-phone" name="phone" value="${user.phone || ''}" placeholder="Enter phone number">
                </div>
                <div class="form-group">
                    <label for="edit-address"><i class="fas fa-map-marker-alt"></i> Address:</label>
                    <textarea id="edit-address" name="address" rows="3" placeholder="Enter your address">${user.address || ''}</textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary-btn" onclick="document.getElementById('user-modal').remove()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn primary-btn">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        `);

        // Add form submit handler
        document.getElementById('edit-profile-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProfileChanges(e.target);
        });
    }

    // Change password
    changePassword() {
        this.showModal('Change Password', `
            <form id="change-password-form" class="profile-form">
                <div class="form-group">
                    <label for="current-password"><i class="fas fa-lock"></i> Current Password:</label>
                    <input type="password" id="current-password" name="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="new-password"><i class="fas fa-key"></i> New Password:</label>
                    <input type="password" id="new-password" name="newPassword" required minlength="6">
                    <small class="form-help">Password must be at least 6 characters long</small>
                </div>
                <div class="form-group">
                    <label for="confirm-password"><i class="fas fa-key"></i> Confirm New Password:</label>
                    <input type="password" id="confirm-password" name="confirmPassword" required>
                </div>
                <div class="password-strength" id="password-strength" style="display: none;">
                    <div class="strength-bar">
                        <div class="strength-fill"></div>
                    </div>
                    <span class="strength-text">Password strength: <span id="strength-level">Weak</span></span>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary-btn" onclick="document.getElementById('user-modal').remove()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn danger-btn">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </div>
            </form>
        `);

        // Add form submit handler
        document.getElementById('change-password-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePasswordChange(e.target);
        });

        // Add password strength checker
        document.getElementById('new-password').addEventListener('input', (e) => {
            this.checkPasswordStrength(e.target.value);
        });

        // Add password confirmation checker
        document.getElementById('confirm-password').addEventListener('input', (e) => {
            this.checkPasswordMatch();
        });
    }

    // Save profile changes
    saveProfileChanges(form) {
        const formData = new FormData(form);
        const updatedUser = {
            ...this.getCurrentUser(),
            first_name: formData.get('firstName'),
            last_name: formData.get('lastName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            updated_at: new Date().toISOString()
        };

        // Update stored user info
        const storage = this.getToken() === localStorage.getItem('token') ? localStorage : sessionStorage;
        storage.setItem('userInfo', JSON.stringify(updatedUser));
        this.currentUser = updatedUser;

        // Show success message
        this.showNotification('Profile updated successfully!', 'success');

        // Close modal
        document.getElementById('user-modal').remove();

        // Update UI
        this.updateUI();

        // In a real application, this would send data to the server
        console.log('Profile updated:', updatedUser);
    }

    // Save password change
    savePasswordChange(form) {
        const formData = new FormData(form);
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        // Validate passwords
        if (newPassword !== confirmPassword) {
            this.showNotification('New passwords do not match!', 'error');
            return;
        }

        if (newPassword.length < 6) {
            this.showNotification('Password must be at least 6 characters long!', 'error');
            return;
        }

        // For demo purposes, we'll just check if current password is not empty
        if (!currentPassword) {
            this.showNotification('Please enter your current password!', 'error');
            return;
        }

        // Show success message
        this.showNotification('Password changed successfully!', 'success');

        // Close modal
        document.getElementById('user-modal').remove();

        // In a real application, this would:
        // 1. Verify current password with server
        // 2. Hash new password
        // 3. Update password in database
        // 4. Possibly force re-login
        console.log('Password change requested for user:', this.getCurrentUser().email);
    }

    // Check password strength
    checkPasswordStrength(password) {
        const strengthIndicator = document.getElementById('password-strength');
        const strengthFill = document.querySelector('.strength-fill');
        const strengthLevel = document.getElementById('strength-level');

        if (!password) {
            strengthIndicator.style.display = 'none';
            return;
        }

        strengthIndicator.style.display = 'block';

        let strength = 0;
        let level = 'Weak';
        let color = '#dc3545';

        // Check password criteria
        if (password.length >= 6) strength++;
        if (password.length >= 10) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        // Determine strength level
        if (strength >= 5) {
            level = 'Very Strong';
            color = '#28a745';
        } else if (strength >= 4) {
            level = 'Strong';
            color = '#20c997';
        } else if (strength >= 3) {
            level = 'Medium';
            color = '#ffc107';
        } else if (strength >= 2) {
            level = 'Fair';
            color = '#fd7e14';
        }

        strengthFill.style.width = `${(strength / 6) * 100}%`;
        strengthFill.style.backgroundColor = color;
        strengthLevel.textContent = level;
        strengthLevel.style.color = color;
    }

    // Check password match
    checkPasswordMatch() {
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const confirmInput = document.getElementById('confirm-password');

        if (confirmPassword && newPassword !== confirmPassword) {
            confirmInput.style.borderColor = '#dc3545';
            confirmInput.style.backgroundColor = '#f8d7da';
        } else {
            confirmInput.style.borderColor = '#ced4da';
            confirmInput.style.backgroundColor = 'white';
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Remove existing notification
        const existingNotification = document.getElementById('auth-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification
        const notification = document.createElement('div');
        notification.id = 'auth-notification';
        notification.className = `auth-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Create global auth manager instance
window.authManager = new AuthManager();

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authManager.init();
});
