<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - RaoufStore</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/cart.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>RaoufStore</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-actions">
                <a href="cart.html" class="cart-icon active">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="login.html" class="login-btn">Login</a>
                    <a href="login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Shopping Cart</h1>
            <div class="breadcrumb">
                <a href="index.html">Home</a> / <span>Shopping Cart</span>
            </div>
        </div>
    </section>

    <section class="cart-section">
        <div class="container">
            <div class="cart-container">
                <div class="cart-items">
                    <h2>Your Cart</h2>
                    
                    <!-- Empty cart message (hidden if cart has items) -->
                    <div id="empty-cart-message" class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>Your cart is empty</h3>
                        <p>Looks like you haven't added anything to your cart yet.</p>
                        <a href="products.html" class="btn primary-btn">Continue Shopping</a>
                    </div>
                    
                    <!-- Cart items table (hidden if cart is empty) -->
                    <div id="cart-items-container" style="display: none;">
                        <table class="cart-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="cart-items-tbody">
                                <!-- Cart items will be added here dynamically -->
                            </tbody>
                        </table>
                        
                        <div class="cart-actions">
                            <div class="coupon-form">
                                <input type="text" id="coupon-code" placeholder="Coupon Code">
                                <button id="apply-coupon" class="btn secondary-btn">Apply Coupon</button>
                            </div>
                            <button id="clear-cart" class="btn secondary-btn">Clear Cart</button>
                        </div>
                    </div>
                </div>
                
                <div class="cart-summary">
                    <h2>Order Summary</h2>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Items</span>
                            <span id="summary-items-count">0</span>
                        </div>
                        <div class="summary-item">
                            <span>Subtotal</span>
                            <span id="summary-subtotal">$0.00</span>
                        </div>
                        <div class="summary-item discount-row" id="discount-row" style="display: none;">
                            <span>Discount</span>
                            <span id="summary-discount">-$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span>Shipping</span>
                            <span id="summary-shipping">$0.00</span>
                        </div>
                        <div class="summary-item total">
                            <span>Total</span>
                            <span id="summary-total">$0.00</span>
                        </div>
                    </div>
                    
                    <button id="checkout-btn" class="btn primary-btn checkout-btn" disabled>
                        Proceed to Checkout
                    </button>
                    
                    <div class="payment-methods">
                        <p>We Accept:</p>
                        <div class="payment-icons">
                            <i class="fab fa-cc-visa"></i>
                            <i class="fab fa-cc-mastercard"></i>
                            <i class="fab fa-cc-amex"></i>
                            <i class="fab fa-cc-paypal"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <section class="recently-viewed">
                <h2 class="section-title">Recently Viewed</h2>
                <div class="products-grid" id="recently-viewed-products">
                    <!-- Recently viewed products will be added dynamically -->
                    <div class="product-card skeleton">
                        <div class="product-image skeleton-img"></div>
                        <div class="product-info">
                            <div class="skeleton-text skeleton-title"></div>
                            <div class="skeleton-text skeleton-price"></div>
                            <div class="skeleton-text skeleton-rating"></div>
                        </div>
                        <div class="product-actions">
                            <div class="skeleton-btn"></div>
                            <div class="skeleton-btn"></div>
                        </div>
                    </div>
                    <div class="product-card skeleton">
                        <div class="product-image skeleton-img"></div>
                        <div class="product-info">
                            <div class="skeleton-text skeleton-title"></div>
                            <div class="skeleton-text skeleton-price"></div>
                            <div class="skeleton-text skeleton-rating"></div>
                        </div>
                        <div class="product-actions">
                            <div class="skeleton-btn"></div>
                            <div class="skeleton-btn"></div>
                        </div>
                    </div>
                    <div class="product-card skeleton">
                        <div class="product-image skeleton-img"></div>
                        <div class="product-info">
                            <div class="skeleton-text skeleton-title"></div>
                            <div class="skeleton-text skeleton-price"></div>
                            <div class="skeleton-text skeleton-rating"></div>
                        </div>
                        <div class="product-actions">
                            <div class="skeleton-btn"></div>
                            <div class="skeleton-btn"></div>
                        </div>
                    </div>
                    <div class="product-card skeleton">
                        <div class="product-image skeleton-img"></div>
                        <div class="product-info">
                            <div class="skeleton-text skeleton-title"></div>
                            <div class="skeleton-text skeleton-price"></div>
                            <div class="skeleton-text skeleton-rating"></div>
                        </div>
                        <div class="product-actions">
                            <div class="skeleton-btn"></div>
                            <div class="skeleton-btn"></div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>RaoufStore</h3>
                    <p>Your one-stop shop for the latest smartphones and accessories.</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <p>123 Main Street, Tech City, TC 12345</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 RaoufStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/data-manager.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/cart.js"></script>
</body>
</html> 