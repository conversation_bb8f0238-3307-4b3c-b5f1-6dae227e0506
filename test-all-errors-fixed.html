<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Errors Fixed - RaoufStore</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .btn.warning { background: #ffc107; color: #000; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .console-output { background: #000; color: #00ff00; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; max-height: 400px; overflow-y: auto; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .test-item { background: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd; }
        .test-item h4 { margin-top: 0; color: #007bff; }
        .test-result { padding: 5px 10px; border-radius: 3px; font-weight: bold; margin: 5px 0; }
        .test-result.pass { background: #d4edda; color: #155724; }
        .test-result.fail { background: #f8d7da; color: #721c24; }
        .test-result.pending { background: #fff3cd; color: #856404; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <h1>🔍 Test Complet - Toutes les Erreurs Corrigées</h1>
    
    <div class="test-card">
        <h2>🎯 Objectif</h2>
        <p>Vérifier que toutes les erreurs identifiées dans le projet RaoufStore ont été corrigées et que le système fonctionne parfaitement.</p>
        
        <div class="progress-bar">
            <div class="progress-fill" id="overall-progress" style="width: 0%"></div>
        </div>
        <div id="overall-status" class="status info">
            Tests en attente...
        </div>
    </div>
    
    <div class="test-card">
        <h2>🔧 Actions de Test</h2>
        
        <button class="btn" onclick="runAllTests()">
            🚀 Lancer Tous les Tests
        </button>
        
        <button class="btn success" onclick="testDependencies()">
            📦 Test Dépendances
        </button>
        
        <button class="btn warning" onclick="testDataManager()">
            📊 Test Data Manager
        </button>
        
        <button class="btn" onclick="testAuthentication()">
            🔐 Test Authentification
        </button>
        
        <button class="btn" onclick="testFunctionality()">
            ⚙️ Test Fonctionnalités
        </button>
        
        <button class="btn danger" onclick="clearResults()">
            🗑️ Vider Résultats
        </button>
    </div>
    
    <div class="test-card">
        <h2>📊 Résultats des Tests</h2>
        <div class="test-grid" id="test-results">
            <!-- Les résultats apparaîtront ici -->
        </div>
    </div>
    
    <div class="test-card">
        <h2>🖥️ Console de Debug</h2>
        <div id="console-output" class="console-output">
            Console de debug - Les logs apparaîtront ici...
        </div>
    </div>
    
    <div class="test-card">
        <h2>🌐 Tests de Navigation</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <button class="btn" onclick="testPage('frontend/index.html')">🏠 Page Accueil</button>
            <button class="btn" onclick="testPage('frontend/products.html')">📱 Page Produits</button>
            <button class="btn" onclick="testPage('frontend/cart.html')">🛒 Page Panier</button>
            <button class="btn" onclick="testPage('frontend/login.html')">🔑 Page Connexion</button>
            <button class="btn" onclick="testPage('frontend/admin/index.html')">🔧 Admin Panel</button>
            <button class="btn" onclick="testPage('frontend/admin/login.html')">🔐 Admin Login</button>
        </div>
    </div>

    <script src="frontend/js/dependency-manager.js"></script>
    <script src="frontend/js/data-manager.js"></script>
    <script src="frontend/js/auth.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        let testResults = document.getElementById('test-results');
        let overallProgress = document.getElementById('overall-progress');
        let overallStatus = document.getElementById('overall-status');
        
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }
        
        function addTestResult(testName, status, details = '') {
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            testItem.innerHTML = `
                <h4>${testName}</h4>
                <div class="test-result ${status}">${status.toUpperCase()}</div>
                ${details ? `<p>${details}</p>` : ''}
            `;
            testResults.appendChild(testItem);
            
            totalTests++;
            if (status === 'pass') passedTests++;
            if (status === 'fail') failedTests++;
            
            updateOverallProgress();
        }
        
        function updateOverallProgress() {
            const progress = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
            overallProgress.style.width = progress + '%';
            
            if (failedTests === 0 && totalTests > 0) {
                overallStatus.className = 'status success';
                overallStatus.innerHTML = `✅ Tous les tests réussis ! (${passedTests}/${totalTests})`;
            } else if (failedTests > 0) {
                overallStatus.className = 'status error';
                overallStatus.innerHTML = `❌ ${failedTests} test(s) échoué(s) sur ${totalTests}`;
            } else {
                overallStatus.className = 'status info';
                overallStatus.innerHTML = 'Tests en cours...';
            }
        }
        
        function clearResults() {
            testResults.innerHTML = '';
            consoleOutput.innerHTML = 'Console vidée...<br>';
            totalTests = 0;
            passedTests = 0;
            failedTests = 0;
            updateOverallProgress();
        }
        
        async function testDependencies() {
            log('🔍 Test du système de dépendances...', 'info');
            
            try {
                // Test DependencyManager
                if (window.DependencyManager) {
                    addTestResult('Dependency Manager', 'pass', 'Système de dépendances chargé');
                    log('✅ DependencyManager disponible', 'success');
                } else {
                    addTestResult('Dependency Manager', 'fail', 'Système de dépendances non trouvé');
                    log('❌ DependencyManager non disponible', 'error');
                }
                
                // Test des fonctions utilitaires
                if (typeof window.safeAsyncCall === 'function') {
                    addTestResult('Safe Async Call', 'pass', 'Fonction de sécurité disponible');
                } else {
                    addTestResult('Safe Async Call', 'fail', 'Fonction de sécurité manquante');
                }
                
                // Test du debug
                if (typeof window.debugDependencies === 'function') {
                    addTestResult('Debug Functions', 'pass', 'Fonctions de debug disponibles');
                    window.debugDependencies();
                } else {
                    addTestResult('Debug Functions', 'fail', 'Fonctions de debug manquantes');
                }
                
            } catch (error) {
                log('❌ Erreur lors du test des dépendances: ' + error.message, 'error');
                addTestResult('Dependencies Test', 'fail', error.message);
            }
        }
        
        async function testDataManager() {
            log('📊 Test du Data Manager...', 'info');
            
            try {
                // Attendre que le data manager soit prêt
                if (window.DependencyManager) {
                    await window.DependencyManager.waitFor(['RaoufStoreData'], 5000);
                }
                
                // Test RaoufStoreData
                if (window.RaoufStoreData && window.RaoufStoreData.initialized) {
                    addTestResult('RaoufStoreData', 'pass', `Initialisé avec ${window.RaoufStoreData.users.length} utilisateurs`);
                    log('✅ RaoufStoreData initialisé', 'success');
                } else {
                    addTestResult('RaoufStoreData', 'fail', 'Non initialisé');
                    log('❌ RaoufStoreData non initialisé', 'error');
                }
                
                // Test des catégories
                if (window.RaoufStoreData.categories && window.RaoufStoreData.categories.length > 0) {
                    addTestResult('Categories', 'pass', `${window.RaoufStoreData.categories.length} catégories chargées`);
                } else {
                    addTestResult('Categories', 'fail', 'Aucune catégorie trouvée');
                }
                
                // Test des marques
                if (window.RaoufStoreData.brands && window.RaoufStoreData.brands.length > 0) {
                    addTestResult('Brands', 'pass', `${window.RaoufStoreData.brands.length} marques chargées`);
                } else {
                    addTestResult('Brands', 'fail', 'Aucune marque trouvée');
                }
                
                // Test des fonctions
                if (typeof window.getAllProducts === 'function') {
                    addTestResult('Data Functions', 'pass', 'Fonctions de données disponibles');
                } else {
                    addTestResult('Data Functions', 'fail', 'Fonctions de données manquantes');
                }
                
            } catch (error) {
                log('❌ Erreur lors du test du Data Manager: ' + error.message, 'error');
                addTestResult('Data Manager Test', 'fail', error.message);
            }
        }
        
        async function testAuthentication() {
            log('🔐 Test du système d\'authentification...', 'info');
            
            try {
                // Test AuthManager
                if (window.authManager) {
                    addTestResult('Auth Manager', 'pass', 'Gestionnaire d\'authentification chargé');
                    log('✅ AuthManager disponible', 'success');
                    
                    // Test des méthodes
                    if (typeof window.authManager.isAuthenticated === 'function') {
                        addTestResult('Auth Methods', 'pass', 'Méthodes d\'authentification disponibles');
                    } else {
                        addTestResult('Auth Methods', 'fail', 'Méthodes d\'authentification manquantes');
                    }
                } else {
                    addTestResult('Auth Manager', 'fail', 'Gestionnaire d\'authentification non trouvé');
                    log('❌ AuthManager non disponible', 'error');
                }
                
            } catch (error) {
                log('❌ Erreur lors du test d\'authentification: ' + error.message, 'error');
                addTestResult('Authentication Test', 'fail', error.message);
            }
        }
        
        async function testFunctionality() {
            log('⚙️ Test des fonctionnalités...', 'info');
            
            try {
                // Test localStorage
                try {
                    localStorage.setItem('test', 'value');
                    localStorage.removeItem('test');
                    addTestResult('LocalStorage', 'pass', 'Stockage local fonctionnel');
                } catch (e) {
                    addTestResult('LocalStorage', 'fail', 'Problème avec le stockage local');
                }
                
                // Test des fonctions utilitaires
                if (typeof window.safeLocalStorageGet === 'function') {
                    addTestResult('Utility Functions', 'pass', 'Fonctions utilitaires disponibles');
                } else {
                    addTestResult('Utility Functions', 'fail', 'Fonctions utilitaires manquantes');
                }
                
                // Test de la gestion d'erreurs
                try {
                    window.addEventListener('error', () => {});
                    addTestResult('Error Handling', 'pass', 'Gestion d\'erreurs configurée');
                } catch (e) {
                    addTestResult('Error Handling', 'fail', 'Problème avec la gestion d\'erreurs');
                }
                
            } catch (error) {
                log('❌ Erreur lors du test des fonctionnalités: ' + error.message, 'error');
                addTestResult('Functionality Test', 'fail', error.message);
            }
        }
        
        async function runAllTests() {
            log('🚀 Lancement de tous les tests...', 'info');
            clearResults();
            
            await testDependencies();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDataManager();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testFunctionality();
            
            log('✅ Tous les tests terminés !', 'success');
        }
        
        function testPage(url) {
            log(`🌐 Test de navigation vers: ${url}`, 'info');
            window.open(`http://localhost:8080/${url}`, '_blank');
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Page de test chargée', 'info');
            
            // Auto-test après 2 secondes
            setTimeout(() => {
                log('🔄 Lancement automatique des tests...', 'info');
                runAllTests();
            }, 2000);
        });
    </script>
</body>
</html>
