<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cart Fix - PhoneHub</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .cart-count { background: red; color: white; padding: 5px 10px; border-radius: 50%; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
        .btn:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; max-height: 300px; overflow-y: auto; }
        .product-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛒 Test de Correction du Panier</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <strong>Compteur du panier : <span class="cart-count">0</span></strong>
        </div>
        
        <div class="product-card">
            <h3>Produit Test 1</h3>
            <p>Prix : $99.99</p>
            <button class="btn add-to-cart-btn" 
                    data-id="test1" 
                    data-name="Produit Test 1" 
                    data-price="99.99" 
                    data-image="test.jpg">
                ➕ Ajouter au panier
            </button>
        </div>
        
        <div class="product-card">
            <h3>Produit Test 2</h3>
            <p>Prix : $149.99</p>
            <button class="btn add-to-cart-btn" 
                    data-id="test2" 
                    data-name="Produit Test 2" 
                    data-price="149.99" 
                    data-image="test2.jpg">
                ➕ Ajouter au panier
            </button>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="btn" onclick="clearCart()">🗑️ Vider le panier</button>
            <button class="btn" onclick="showCart()">👁️ Voir le panier</button>
        </div>
        
        <div class="log" id="log">
            <strong>📋 Journal des événements :</strong><br>
            <div id="log-content">Prêt pour les tests...</div>
        </div>
    </div>

    <script>
        let eventCount = 0;
        
        function log(message) {
            eventCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logContent = document.getElementById('log-content');
            logContent.innerHTML += `<br>[${timestamp}] #${eventCount}: ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function updateCartDisplay() {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            const count = cart.reduce((total, item) => total + item.quantity, 0);
            document.querySelector('.cart-count').textContent = count;
            log(`Compteur mis à jour: ${count} articles`);
        }
        
        function clearCart() {
            localStorage.removeItem('cart');
            updateCartDisplay();
            log('Panier vidé');
        }
        
        function showCart() {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            log(`Contenu du panier: ${JSON.stringify(cart, null, 2)}`);
        }
        
        // Simuler la fonction handleAddToCart
        window.handleAddToCart = function(event) {
            log('🔥 handleAddToCart appelée');
            
            const button = event.target.closest('.add-to-cart-btn');
            if (!button) {
                log('❌ Bouton non trouvé');
                return;
            }
            
            // Vérifier le flag de traitement
            if (button.dataset.processing === 'true') {
                log('⚠️ Traitement déjà en cours, ignoré');
                return;
            }
            button.dataset.processing = 'true';
            
            const productId = button.dataset.id;
            const productName = button.dataset.name;
            const productPrice = parseFloat(button.dataset.price);
            
            log(`📦 Ajout: ${productName} (${productId}) - $${productPrice}`);
            
            // Récupérer le panier
            let cart = JSON.parse(localStorage.getItem('cart') || '[]');
            
            // Vérifier si le produit existe déjà
            const existingItemIndex = cart.findIndex(item => item.id === productId);
            
            if (existingItemIndex >= 0) {
                cart[existingItemIndex].quantity += 1;
                log(`✅ Quantité mise à jour: ${cart[existingItemIndex].quantity}`);
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    quantity: 1
                });
                log('✅ Nouvel article ajouté');
            }
            
            // Sauvegarder
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartDisplay();
            
            // Réinitialiser le flag
            setTimeout(() => {
                button.dataset.processing = 'false';
                log('🔄 Flag de traitement réinitialisé');
            }, 100);
        };
        
        // Gestionnaire d'événements global
        document.addEventListener('click', function(event) {
            const addToCartBtn = event.target.closest('.add-to-cart-btn');
            if (addToCartBtn) {
                log('👆 Clic détecté sur bouton add-to-cart');
                event.preventDefault();
                event.stopPropagation();
                handleAddToCart(event);
            }
        });
        
        // Initialisation
        updateCartDisplay();
        log('🚀 Test initialisé');
    </script>
</body>
</html>
