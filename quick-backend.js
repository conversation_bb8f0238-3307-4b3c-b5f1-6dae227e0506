// Backend rapide pour RaoufStore
const express = require('express');
const app = express();
const PORT = 3002;

// Middleware
app.use(express.json());
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Log des requêtes
app.use((req, res, next) => {
    console.log(`${new Date().toLocaleTimeString()} - ${req.method} ${req.url}`);
    next();
});

// Routes API
app.get('/api', (req, res) => {
    res.json({
        message: 'Welcome to RaoufStore E-Commerce API',
        status: 'Running',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            health: '/api/health',
            products: '/api/products',
            users: '/api/users'
        }
    });
});

app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'RaoufStore API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

app.get('/api/products', (req, res) => {
    res.json({
        success: true,
        products: [
            {
                id: 1,
                name: 'iPhone 13 Pro Max',
                price: 147000,
                description: 'Apple flagship smartphone',
                brand: 'Apple',
                category: 'Smartphones',
                stock: 50,
                image: 'images/real-products/iphone13_main.jpg'
            },
            {
                id: 2,
                name: 'Samsung Galaxy S21',
                price: 160000,
                description: 'Samsung flagship smartphone',
                brand: 'Samsung',
                category: 'Smartphones',
                stock: 30,
                image: 'images/real-products/samsung_s21_main.jpg'
            },
            {
                id: 3,
                name: 'Google Pixel 6',
                price: 120000,
                description: 'Google flagship smartphone',
                brand: 'Google',
                category: 'Smartphones',
                stock: 25,
                image: 'images/real-products/pixel6_main.jpg'
            }
        ]
    });
});

app.get('/api/users', (req, res) => {
    res.json({
        success: true,
        message: 'Users endpoint - Authentication required',
        users: []
    });
});

// Gestion d'erreurs
app.use((err, req, res, next) => {
    console.error('Erreur API:', err);
    res.status(500).json({
        success: false,
        message: 'Erreur serveur',
        error: err.message
    });
});

// Route 404
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        url: req.originalUrl
    });
});

// Démarrer le serveur
app.listen(PORT, () => {
    console.log('🚀 RaoufStore Backend démarré !');
    console.log(`✅ Serveur actif sur: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/api`);
    console.log(`💚 Health: http://localhost:${PORT}/api/health`);
    console.log(`📦 Products: http://localhost:${PORT}/api/products`);
    console.log('⏹️  Pour arrêter: Ctrl+C');
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  Port ${PORT} déjà utilisé`);
        console.log(`🔗 Backend probablement déjà actif sur: http://localhost:${PORT}/api`);
    } else {
        console.error('❌ Erreur serveur:', err);
    }
});

// Gestion arrêt propre
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du backend RaoufStore...');
    process.exit(0);
});
