// Script simple pour démarrer frontend et backend RaoufStore
const express = require('express');
const path = require('path');

console.log('🚀 Démarrage des serveurs RaoufStore...');

// ===== SERVEUR FRONTEND =====
const frontendApp = express();
const FRONTEND_PORT = 8080;

// Middleware CORS simple
frontendApp.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Servir les fichiers statiques
frontendApp.use(express.static(path.join(__dirname, 'frontend')));
frontendApp.use('/frontend', express.static(path.join(__dirname, 'frontend')));

// Routes principales
frontendApp.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

// Démarrer frontend
frontendApp.listen(FRONTEND_PORT, () => {
    console.log(`✅ Frontend démarré: http://localhost:${FRONTEND_PORT}`);
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  Frontend déjà actif sur port ${FRONTEND_PORT}`);
    }
});

// ===== SERVEUR BACKEND =====
const backendApp = express();
const BACKEND_PORT = 3001;

// Middleware
backendApp.use(express.json());
backendApp.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Log des requêtes
backendApp.use((req, res, next) => {
    console.log(`API: ${req.method} ${req.url}`);
    next();
});

// Routes API
backendApp.get('/api', (req, res) => {
    res.json({
        message: 'Welcome to RaoufStore E-Commerce API',
        status: 'Running',
        timestamp: new Date().toISOString()
    });
});

backendApp.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'RaoufStore API is healthy',
        timestamp: new Date().toISOString()
    });
});

backendApp.get('/api/products', (req, res) => {
    res.json({
        success: true,
        products: [
            {
                id: 1,
                name: 'iPhone 13 Pro Max',
                price: 1099.99,
                description: 'Apple flagship smartphone',
                brand: 'Apple',
                category: 'Smartphones'
            },
            {
                id: 2,
                name: 'Samsung Galaxy S21',
                price: 799.99,
                description: 'Samsung flagship smartphone',
                brand: 'Samsung',
                category: 'Smartphones'
            }
        ]
    });
});

// Démarrer backend
backendApp.listen(BACKEND_PORT, () => {
    console.log(`✅ Backend démarré: http://localhost:${BACKEND_PORT}/api`);
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  Backend déjà actif sur port ${BACKEND_PORT}`);
    }
});

// Affichage des informations
setTimeout(() => {
    console.log('\n🎉 RAOUFSTORE LANCÉ AVEC SUCCÈS !');
    console.log('=' * 50);
    console.log('🌐 FRONTEND (Site Web):');
    console.log(`   📱 Accueil: http://localhost:${FRONTEND_PORT}/frontend/index.html`);
    console.log(`   🛒 Produits: http://localhost:${FRONTEND_PORT}/frontend/products.html`);
    console.log(`   🔧 Admin: http://localhost:${FRONTEND_PORT}/frontend/admin/index.html`);
    console.log('');
    console.log('🔗 BACKEND (API):');
    console.log(`   🔗 API: http://localhost:${BACKEND_PORT}/api`);
    console.log(`   💚 Health: http://localhost:${BACKEND_PORT}/api/health`);
    console.log(`   📦 Products: http://localhost:${BACKEND_PORT}/api/products`);
    console.log('');
    console.log('👤 COMPTES DE TEST:');
    console.log('   🔑 Admin: <EMAIL> / Admin@123');
    console.log('   👤 Client: <EMAIL> / User@123');
    console.log('');
    console.log('⏹️  Pour arrêter: Ctrl+C');
    console.log('=' * 50);
}, 1000);

// Gestion arrêt propre
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt des serveurs RaoufStore...');
    process.exit(0);
});
