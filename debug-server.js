const express = require('./backend/node_modules/express');
const cors = require('./backend/node_modules/cors');
const { Pool } = require('./backend/node_modules/pg');

const app = express();
const PORT = 3001; // Use different port

// Database connection
const pool = new Pool({
  user: 'postgres',
  password: 'r<PERSON><PERSON><PERSON><PERSON>@',
  host: 'localhost',
  port: 5432,
  database: 'ecommerce_phones'
});

// Middleware
app.use(cors());
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Test database connection
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('Testing database connection...');
    const result = await pool.query('SELECT NOW()');
    console.log('Database connection successful');
    res.json({ success: true, time: result.rows[0].now });
  } catch (error) {
    console.error('Database connection failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Simple products endpoint
app.get('/api/products', async (req, res) => {
  try {
    console.log('Fetching products...');
    
    const result = await pool.query(`
      SELECT p.*, b.name as brand_name, c.name as category_name
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.brand_id
      LEFT JOIN categories c ON p.category_id = c.category_id
      LIMIT 10
    `);
    
    console.log(`Found ${result.rows.length} products`);
    
    res.json({
      success: true,
      products: result.rows
    });
  } catch (error) {
    console.error('Products query failed:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error while fetching products',
      error: error.message 
    });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ status: 'OK', message: 'Debug server is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Debug server running on port ${PORT}`);
  console.log(`📡 API accessible at http://localhost:${PORT}/api`);
});

// Test database on startup
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Error acquiring client:', err.stack);
    return;
  }
  console.log('✅ Connected to PostgreSQL database');
  release();
});
