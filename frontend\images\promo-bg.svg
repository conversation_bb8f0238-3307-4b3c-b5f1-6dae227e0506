<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="600" viewBox="0 0 1200 600">
  <!-- Background Pattern -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6C63FF;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#3F51B5;stop-opacity:0.2" />
    </linearGradient>
    <pattern id="dots-pattern" width="30" height="30" patternUnits="userSpaceOnUse">
      <circle cx="15" cy="15" r="2" fill="#FFFFFF" opacity="0.3" />
    </pattern>
    <pattern id="grid-pattern" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.2" />
    </pattern>
  </defs>
  
  <!-- Main Background -->
  <rect width="1200" height="600" fill="url(#bg-gradient)" />
  
  <!-- Background Patterns -->
  <rect width="1200" height="600" fill="url(#dots-pattern)" />
  <rect width="1200" height="600" fill="url(#grid-pattern)" />
  
  <!-- Abstract Shapes -->
  <circle cx="200" cy="450" r="300" fill="#4A46B4" opacity="0.2" />
  <circle cx="950" cy="150" r="250" fill="#7C4DFF" opacity="0.2" />
  <circle cx="600" cy="500" r="180" fill="#651FFF" opacity="0.15" />
  
  <!-- Wavy Lines -->
  <path d="M0,150 Q300,100 600,150 T1200,100" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.1" />
  <path d="M0,300 Q300,250 600,300 T1200,250" fill="none" stroke="#FFFFFF" stroke-width="3" opacity="0.1" />
  <path d="M0,450 Q300,400 600,450 T1200,400" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.1" />
  
  <!-- Phone Outlines -->
  <g transform="translate(900, 300)">
    <rect x="-30" y="-70" width="60" height="140" rx="10" ry="10" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.5" />
    <rect x="-25" y="-60" width="50" height="110" rx="5" ry="5" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.4" />
    <circle cx="0" cy="65" r="6" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.4" />
  </g>
  
  <g transform="translate(100, 250) rotate(-15)">
    <rect x="-35" y="-80" width="70" height="160" rx="12" ry="12" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.5" />
    <rect x="-30" y="-70" width="60" height="130" rx="6" ry="6" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.4" />
    <circle cx="0" cy="70" r="7" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.4" />
  </g>
  
  <!-- Diagonal Lines -->
  <g opacity="0.1">
    <line x1="0" y1="0" x2="1200" y2="600" stroke="#FFFFFF" stroke-width="5" />
    <line x1="200" y1="0" x2="1200" y2="500" stroke="#FFFFFF" stroke-width="3" />
    <line x1="400" y1="0" x2="1200" y2="400" stroke="#FFFFFF" stroke-width="2" />
    <line x1="0" y1="100" x2="1000" y2="600" stroke="#FFFFFF" stroke-width="4" />
    <line x1="0" y1="200" x2="800" y2="600" stroke="#FFFFFF" stroke-width="3" />
  </g>
</svg> 