<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication System - PhoneHub</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .test-button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .test-button.secondary { background: #6c757d; }
        .test-button.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .product-test { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="frontend/index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>PhoneHub</span>
                </a>
            </div>
            <div class="header-actions">
                <a href="frontend/cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="frontend/login.html" class="login-btn">Login</a>
                    <a href="frontend/login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <div class="test-container">
        <h1>🔐 Test du Système d'Authentification</h1>
        
        <div class="test-section">
            <h2>📊 État Actuel</h2>
            <div id="auth-status" class="status warning">Vérification en cours...</div>
            <div id="user-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Tests d'Authentification</h2>
            <button class="test-button" onclick="testLogin()">🔑 Test Login Admin</button>
            <button class="test-button secondary" onclick="testLogout()">🚪 Test Logout</button>
            <button class="test-button" onclick="checkAuthStatus()">🔍 Vérifier État Auth</button>
        </div>
        
        <div class="test-section">
            <h2>🛒 Tests du Panier</h2>
            <p>Ces tests vérifient que l'authentification est requise pour ajouter au panier :</p>
            
            <div class="product-test">
                <h4>Produit Test 1</h4>
                <p>iPhone 13 Pro Max - $1099.99</p>
                <button class="btn primary-btn add-to-cart-btn" 
                        data-id="test1" 
                        data-name="iPhone 13 Pro Max" 
                        data-price="1099.99" 
                        data-image="frontend/images/placeholder.svg">
                    <i class="fas fa-cart-plus"></i> Ajouter au Panier
                </button>
            </div>
            
            <div class="product-test">
                <h4>Produit Test 2</h4>
                <p>Samsung Galaxy S21 - $799.99</p>
                <button class="btn primary-btn add-to-cart-btn" 
                        data-id="test2" 
                        data-name="Samsung Galaxy S21" 
                        data-price="799.99" 
                        data-image="frontend/images/placeholder.svg">
                    <i class="fas fa-cart-plus"></i> Ajouter au Panier
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Tests de Navigation</h2>
            <a href="http://localhost:8080/frontend/cart.html" class="test-button">🛒 Accéder au Panier</a>
            <a href="http://localhost:8080/frontend/login.html" class="test-button">🔑 Page de Connexion</a>
            <a href="http://localhost:8080" class="test-button secondary">🏠 Retour Accueil</a>
        </div>
        
        <div class="test-section">
            <h2>📋 Checklist de Vérification</h2>
            <ul id="checklist">
                <li>❓ Boutons Login/Register visibles quand non connecté</li>
                <li>❓ Menu utilisateur visible quand connecté</li>
                <li>❓ Modal d'authentification s'affiche pour ajout panier</li>
                <li>❓ Page panier protégée par authentification</li>
                <li>❓ Compteur panier mis à jour après connexion</li>
                <li>❓ Panier spécifique à l'utilisateur connecté</li>
                <li>❓ Checkout nécessite une authentification</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📝 Journal des Événements</h2>
            <div id="event-log" style="background: white; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
            <button class="test-button secondary" onclick="clearLog()">🗑️ Vider le Journal</button>
        </div>
    </div>

    <script src="frontend/js/auth.js"></script>
    <script src="frontend/js/app.js"></script>
    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('event-log');
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[AUTH TEST] ${message}`);
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        function updateAuthStatus() {
            const statusElement = document.getElementById('auth-status');
            const userInfoElement = document.getElementById('user-info');
            
            if (window.authManager && window.authManager.isAuthenticated()) {
                const user = window.authManager.getCurrentUser();
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Utilisateur connecté';
                userInfoElement.innerHTML = `
                    <strong>Utilisateur :</strong> ${user.first_name} ${user.last_name}<br>
                    <strong>Email :</strong> ${user.email}<br>
                    <strong>Rôle :</strong> ${user.role}<br>
                    <strong>ID :</strong> ${user.user_id}
                `;
                log(`Utilisateur connecté: ${user.email} (${user.role})`);
            } else {
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ Utilisateur non connecté';
                userInfoElement.innerHTML = '<em>Aucun utilisateur connecté</em>';
                log('Aucun utilisateur connecté');
            }
        }

        function testLogin() {
            log('Test de connexion admin...');
            // Simulate admin login
            const token = btoa('<EMAIL>:admin:' + Date.now());
            const userInfo = {
                user_id: 1,
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin'
            };
            
            if (window.authManager) {
                window.authManager.login(token, userInfo, false);
                log('Connexion admin simulée avec succès');
                updateAuthStatus();
            } else {
                log('❌ AuthManager non disponible');
            }
        }

        function testLogout() {
            log('Test de déconnexion...');
            if (window.authManager) {
                window.authManager.logout();
                log('Déconnexion effectuée');
                updateAuthStatus();
            } else {
                log('❌ AuthManager non disponible');
            }
        }

        function checkAuthStatus() {
            log('Vérification de l\'état d\'authentification...');
            updateAuthStatus();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Page de test chargée');
            setTimeout(updateAuthStatus, 500);
            
            // Listen for auth events
            window.addEventListener('userLoggedIn', (event) => {
                log(`Événement: Utilisateur connecté - ${event.detail.email}`);
                updateAuthStatus();
            });
            
            window.addEventListener('userLoggedOut', () => {
                log('Événement: Utilisateur déconnecté');
                updateAuthStatus();
            });
        });
    </script>
</body>
</html>
