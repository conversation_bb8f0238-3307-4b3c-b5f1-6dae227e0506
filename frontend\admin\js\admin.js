document.addEventListener('DOMContentLoaded', () => {
    // Global variables
    window.API_URL = 'http://localhost:3000/api';
    window.ADMIN_VALIDATE_URL = `${window.API_URL}/users/validate-admin`;

    // Check authentication on admin pages
    if (!window.location.pathname.includes('login.html')) {
        checkAdminAuthentication();
    }

    // Logout functionality is now handled in login.js

    // Notification system
    window.showNotification = function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            hideNotification(notification);
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            clearTimeout(hideTimeout);
            hideNotification(notification);
        });
    };

    function hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }

    // Check admin authentication
    async function checkAdminAuthentication() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (!token || !user) {
            window.location.href = '../login.html';
            return;
        }

        try {
            const userData = JSON.parse(user);
            if (userData.role !== 'admin') {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '../login.html';
                return;
            }

            // Verify token is still valid by making a test API call
            const response = await fetch(`${window.API_URL}/admin/dashboard`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '../login.html';
                return;
            }

            // Mark auth as ready for dependency manager
            if (window.DependencyManager) {
                window.DependencyManager.markReady('auth');
                window.DependencyManager.markReady('api');
            }

        } catch (error) {
            console.error('Authentication check failed:', error);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '../login.html';
        }
    }
});

// API utility functions
async function apiRequest(endpoint, method = 'GET', data = null) {
    const token = localStorage.getItem('token') || localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');

    if (!token) {
        window.location.href = 'login.html';
        return null;
    }

    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(`${window.API_URL}${endpoint}`, options);

        if (response.status === 401) {
            // Redirect to login page
            localStorage.removeItem('token');
            localStorage.removeItem('adminToken');
            sessionStorage.removeItem('adminToken');
            window.location.href = '../login.html';
            return null;
        }

        return await response.json();
    } catch (error) {
        console.error(`API Error (${endpoint}):`, error);
        if (window.showNotification) {
            window.showNotification('An error occurred while communicating with the server.', 'error');
        }
        throw error; // Re-throw to allow caller to handle
    }
}

// Format utilities
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

function formatCurrency(amount) {
    return parseFloat(amount).toLocaleString() + ' DZD';
}

// Common data loading functions
async function loadProductCount() {
    const productCountElement = document.getElementById('product-count');
    if (!productCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        productCountElement.textContent = stats.productCount || 0;
    }
}

async function loadUserCount() {
    const userCountElement = document.getElementById('user-count');
    if (!userCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        userCountElement.textContent = stats.userCount || 0;
    }
}

async function loadOrderCount() {
    const orderCountElement = document.getElementById('order-count');
    if (!orderCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        orderCountElement.textContent = stats.orderCount || 0;
    }
}

async function loadRevenue() {
    const revenueElement = document.getElementById('revenue');
    if (!revenueElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        revenueElement.textContent = formatCurrency(stats.totalRevenue || 0);
    }
}