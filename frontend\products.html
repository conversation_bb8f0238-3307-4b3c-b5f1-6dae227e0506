<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - RaoufStore</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/products.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>RaoufStore</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Products</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-actions">
                <a href="cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <div class="auth-buttons">
                    <a href="login.html" class="login-btn">Login</a>
                    <a href="login.html?tab=register" class="register-btn">Register</a>
                </div>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>All Products</h1>
            <div class="breadcrumb">
                <a href="index.html">Home</a> / <span>Products</span>
            </div>
        </div>
    </section>

    <section class="products-section">
        <div class="container">
            <div class="products-container">
                <div class="filters-sidebar">
                    <div class="filter-group">
                        <h3>Categories</h3>
                        <ul class="filter-list" id="category-filters">
                            <li>
                                <label>
                                    <input type="checkbox" value="1" name="category"> Smartphones
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="2" name="category"> Tablets
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="3" name="category"> Accessories
                                </label>
                            </li>
                        </ul>
                    </div>

                    <div class="filter-group">
                        <h3>Brands</h3>
                        <ul class="filter-list" id="brand-filters">
                            <li>
                                <label>
                                    <input type="checkbox" value="1" name="brand"> Apple
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="2" name="brand"> Samsung
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="3" name="brand"> Google
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="4" name="brand"> OnePlus
                                </label>
                            </li>
                            <li>
                                <label>
                                    <input type="checkbox" value="5" name="brand"> Xiaomi
                                </label>
                            </li>
                        </ul>
                    </div>

                    <div class="filter-group">
                        <h3>Price Range</h3>
                        <div class="price-range">
                            <input type="range" id="price-slider" min="0" max="200000" step="5000" value="200000">
                            <div class="price-values">
                                <span>0 DZD</span>
                                <span id="price-value">200,000+ DZD</span>
                            </div>
                        </div>
                    </div>

                    <button id="apply-filters" class="btn primary-btn">Apply Filters</button>
                    <button id="clear-filters" class="btn secondary-btn">Clear Filters</button>
                </div>

                <div class="products-main">
                    <div class="products-header">
                        <div class="products-count">
                            <span id="products-total">0</span> Products
                        </div>
                        <div class="products-sort">
                            <label for="sort-by">Sort by:</label>
                            <select id="sort-by">
                                <option value="newest">Newest</option>
                                <option value="price_asc">Price: Low to High</option>
                                <option value="price_desc">Price: High to Low</option>
                                <option value="name_asc">Name: A to Z</option>
                                <option value="name_desc">Name: Z to A</option>
                            </select>
                        </div>
                    </div>

                    <div class="products-grid" id="products-grid">
                        <!-- Products will be loaded here dynamically -->
                        <div class="product-card skeleton">
                            <div class="product-image skeleton-img">
                                <img src="images/real-products/iphone13_main.jpg" alt="Loading" style="opacity: 0.2;">
                            </div>
                            <div class="product-info">
                                <div class="skeleton-text skeleton-title"></div>
                                <div class="skeleton-text skeleton-price"></div>
                                <div class="skeleton-text skeleton-rating"></div>
                            </div>
                            <div class="product-actions">
                                <div class="skeleton-btn"></div>
                                <div class="skeleton-btn"></div>
                            </div>
                        </div>
                        <div class="product-card skeleton">
                            <div class="product-image skeleton-img">
                                <img src="images/real-products/samsung_s21_main.jpg" alt="Loading" style="opacity: 0.2;">
                            </div>
                            <div class="product-info">
                                <div class="skeleton-text skeleton-title"></div>
                                <div class="skeleton-text skeleton-price"></div>
                                <div class="skeleton-text skeleton-rating"></div>
                            </div>
                            <div class="product-actions">
                                <div class="skeleton-btn"></div>
                                <div class="skeleton-btn"></div>
                            </div>
                        </div>
                        <div class="product-card skeleton">
                            <div class="product-image skeleton-img">
                                <img src="images/real-products/pixel6_main.jpg" alt="Loading" style="opacity: 0.2;">
                            </div>
                            <div class="product-info">
                                <div class="skeleton-text skeleton-title"></div>
                                <div class="skeleton-text skeleton-price"></div>
                                <div class="skeleton-text skeleton-rating"></div>
                            </div>
                            <div class="product-actions">
                                <div class="skeleton-btn"></div>
                                <div class="skeleton-btn"></div>
                            </div>
                        </div>
                        <div class="product-card skeleton">
                            <div class="product-image skeleton-img">
                                <img src="images/real-products/oneplus9_main.jpg" alt="Loading" style="opacity: 0.2;">
                            </div>
                            <div class="product-info">
                                <div class="skeleton-text skeleton-title"></div>
                                <div class="skeleton-text skeleton-price"></div>
                                <div class="skeleton-text skeleton-rating"></div>
                            </div>
                            <div class="product-actions">
                                <div class="skeleton-btn"></div>
                                <div class="skeleton-btn"></div>
                            </div>
                        </div>
                    </div>

                    <div class="pagination" id="pagination">
                        <button class="pagination-btn" data-page="prev">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="pagination-numbers" id="pagination-numbers">
                            <button class="pagination-number active">1</button>
                            <button class="pagination-number">2</button>
                            <button class="pagination-number">3</button>
                        </div>
                        <button class="pagination-btn" data-page="next">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>RaoufStore</h3>
                    <p>Your one-stop shop for the latest smartphones and accessories.</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <p>123 Main Street, Tech City, TC 12345</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 RaoufStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/dependency-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/products.js"></script>
</body>
</html> 