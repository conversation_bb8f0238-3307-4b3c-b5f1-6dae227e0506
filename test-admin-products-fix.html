<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Products Fix - RaoufStore</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .console-output { background: #000; color: #00ff00; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; max-height: 300px; overflow-y: auto; }
        .select-test { margin: 15px 0; }
        .select-test select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .select-test label { display: block; margin: 5px 0; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 Test Admin Products - Categories & Brands Fix</h1>
    
    <div class="test-card">
        <h2>🎯 Problème à Résoudre</h2>
        <div class="status error">
            <strong>Problème :</strong> Les champs catégorie et marque sont vides dans la page d'ajout de produit admin.
        </div>
        <p><strong>Cause :</strong> Les fonctions loadCategories() et loadBrands() essayaient de charger depuis des endpoints API inexistants.</p>
        <p><strong>Solution :</strong> Utiliser les données du data manager local.</p>
    </div>
    
    <div class="test-card">
        <h2>🔧 Test des Corrections</h2>
        
        <button class="btn" onclick="testDataManager()">
            📊 Test Data Manager
        </button>
        
        <button class="btn" onclick="testCategoriesLoad()">
            📂 Test Catégories
        </button>
        
        <button class="btn" onclick="testBrandsLoad()">
            🏷️ Test Marques
        </button>
        
        <button class="btn success" onclick="goToAdminProducts()">
            🔧 Aller à Admin Products
        </button>
    </div>
    
    <div class="test-card">
        <h2>📊 Statut des Données</h2>
        <div id="data-status" class="status info">
            Cliquez sur "Test Data Manager" pour vérifier.
        </div>
    </div>
    
    <div class="test-card">
        <h2>🧪 Test des Selects</h2>
        <div class="select-test">
            <label for="test-category">Test Catégorie :</label>
            <select id="test-category">
                <option value="">Chargement...</option>
            </select>
        </div>
        
        <div class="select-test">
            <label for="test-brand">Test Marque :</label>
            <select id="test-brand">
                <option value="">Chargement...</option>
            </select>
        </div>
        
        <button class="btn" onclick="populateTestSelects()">
            🔄 Remplir les Selects de Test
        </button>
    </div>
    
    <div class="test-card">
        <h2>🖥️ Console Output</h2>
        <div id="console-output" class="console-output">
            Console logs apparaîtront ici...
        </div>
    </div>

    <script src="frontend/js/data-manager.js"></script>
    <script>
        let consoleOutput = document.getElementById('console-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }
        
        async function testDataManager() {
            log('🔍 Test du Data Manager...');
            
            if (window.RaoufStoreData) {
                log('✅ RaoufStoreData existe');
                log(`📊 Initialisé: ${window.RaoufStoreData.initialized}`);
                log(`📂 Catégories: ${window.RaoufStoreData.categories?.length || 0}`);
                log(`🏷️ Marques: ${window.RaoufStoreData.brands?.length || 0}`);
                log(`📱 Produits: ${window.RaoufStoreData.products?.length || 0}`);
                
                updateDataStatus();
            } else {
                log('❌ RaoufStoreData non trouvé');
                document.getElementById('data-status').className = 'status error';
                document.getElementById('data-status').innerHTML = '<strong>❌ Data Manager non trouvé</strong>';
            }
        }
        
        function updateDataStatus() {
            const statusDiv = document.getElementById('data-status');
            
            if (window.RaoufStoreData?.categories?.length > 0 && window.RaoufStoreData?.brands?.length > 0) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    <strong>✅ Data Manager OK !</strong><br>
                    Catégories: ${window.RaoufStoreData.categories.length} | 
                    Marques: ${window.RaoufStoreData.brands.length} | 
                    Produits: ${window.RaoufStoreData.products.length}
                `;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <strong>❌ Données manquantes !</strong><br>
                    Catégories: ${window.RaoufStoreData?.categories?.length || 0} | 
                    Marques: ${window.RaoufStoreData?.brands?.length || 0}
                `;
            }
        }
        
        async function testCategoriesLoad() {
            log('📂 Test du chargement des catégories...');
            
            try {
                // Simuler la fonction loadCategories corrigée
                let attempts = 0;
                while ((!window.RaoufStoreData || !window.RaoufStoreData.categories) && attempts < 50) {
                    log(`⏳ Attente data manager... tentative ${attempts + 1}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (window.RaoufStoreData?.categories) {
                    log(`✅ Catégories chargées: ${window.RaoufStoreData.categories.length}`);
                    window.RaoufStoreData.categories.forEach(cat => {
                        log(`  📂 ${cat.id}: ${cat.name}`);
                    });
                } else {
                    log('⚠️ Utilisation des catégories de fallback');
                    const fallbackCategories = [
                        { id: 1, name: 'iPhone' },
                        { id: 2, name: 'Samsung' },
                        { id: 3, name: 'Google' },
                        { id: 4, name: 'OnePlus' },
                        { id: 5, name: 'Xiaomi' },
                        { id: 6, name: 'Tablets' },
                        { id: 7, name: 'Accessories' },
                        { id: 8, name: 'Wearables' }
                    ];
                    fallbackCategories.forEach(cat => {
                        log(`  📂 ${cat.id}: ${cat.name} (fallback)`);
                    });
                }
            } catch (error) {
                log('❌ Erreur test catégories: ' + error.message);
            }
        }
        
        async function testBrandsLoad() {
            log('🏷️ Test du chargement des marques...');
            
            try {
                // Simuler la fonction loadBrands corrigée
                let attempts = 0;
                while ((!window.RaoufStoreData || !window.RaoufStoreData.brands) && attempts < 50) {
                    log(`⏳ Attente data manager... tentative ${attempts + 1}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (window.RaoufStoreData?.brands) {
                    log(`✅ Marques chargées: ${window.RaoufStoreData.brands.length}`);
                    window.RaoufStoreData.brands.forEach(brand => {
                        log(`  🏷️ ${brand.id}: ${brand.name}`);
                    });
                } else {
                    log('⚠️ Utilisation des marques de fallback');
                    const fallbackBrands = [
                        { id: 1, name: 'Apple' },
                        { id: 2, name: 'Samsung' },
                        { id: 3, name: 'Google' },
                        { id: 4, name: 'OnePlus' },
                        { id: 5, name: 'Xiaomi' },
                        { id: 6, name: 'Huawei' },
                        { id: 7, name: 'Sony' },
                        { id: 8, name: 'LG' }
                    ];
                    fallbackBrands.forEach(brand => {
                        log(`  🏷️ ${brand.id}: ${brand.name} (fallback)`);
                    });
                }
            } catch (error) {
                log('❌ Erreur test marques: ' + error.message);
            }
        }
        
        async function populateTestSelects() {
            log('🔄 Remplissage des selects de test...');
            
            const categorySelect = document.getElementById('test-category');
            const brandSelect = document.getElementById('test-brand');
            
            // Attendre que les données soient prêtes
            await testDataManager();
            
            // Remplir catégories
            if (window.RaoufStoreData?.categories) {
                categorySelect.innerHTML = '<option value="">Sélectionner une catégorie</option>';
                window.RaoufStoreData.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
                log('✅ Select catégories rempli');
            }
            
            // Remplir marques
            if (window.RaoufStoreData?.brands) {
                brandSelect.innerHTML = '<option value="">Sélectionner une marque</option>';
                window.RaoufStoreData.brands.forEach(brand => {
                    const option = document.createElement('option');
                    option.value = brand.id;
                    option.textContent = brand.name;
                    brandSelect.appendChild(option);
                });
                log('✅ Select marques rempli');
            }
        }
        
        function goToAdminProducts() {
            log('🔧 Redirection vers Admin Products...');
            window.location.href = 'http://localhost:8081/frontend/admin/products.html';
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Page de test chargée');
            
            // Auto-test après 2 secondes
            setTimeout(() => {
                log('🔄 Test automatique...');
                testDataManager();
                populateTestSelects();
            }, 2000);
        });
    </script>
</body>
</html>
