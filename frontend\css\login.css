/* Login and Registration Page Styles */

.login-section {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.auth-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-tabs {
    display: flex;
    border-bottom: 1px solid #e1e5eb;
}

.auth-tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
}

.auth-tab.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

.auth-tab:hover {
    background-color: #f8f9fa;
}

.auth-content {
    padding: 30px;
}

.auth-tab-content {
    display: none;
}

.auth-tab-content.active {
    display: block;
}

.auth-tab-content h2 {
    margin-bottom: 25px;
    text-align: center;
    font-size: 24px;
    color: #343a40;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: #007bff;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

button.primary-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
}

.social-login {
    margin-top: 25px;
    text-align: center;
}

.social-login p {
    position: relative;
    margin-bottom: 20px;
    color: #6c757d;
}

.social-login p::before,
.social-login p::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background-color: #dee2e6;
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 15px;
}

.social-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.social-btn i {
    margin-right: 10px;
}

.google-btn {
    background-color: #fff;
    color: #757575;
    border: 1px solid #ddd;
}

.google-btn:hover {
    background-color: #f1f1f1;
}

.facebook-btn {
    background-color: #3b5998;
    color: #fff;
}

.facebook-btn:hover {
    background-color: #2d4373;
}

.password-strength {
    margin-top: 8px;
}

.strength-meter {
    height: 4px;
    width: 0;
    background-color: #ddd;
    border-radius: 2px;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-text {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6c757d;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    font-size: 14px;
}

.checkbox-group input {
    margin-right: 8px;
    margin-top: 3px;
}

.checkbox-group a {
    color: #007bff;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-container {
        max-width: 100%;
        margin: 0 15px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .social-buttons {
        flex-direction: column;
    }
} 