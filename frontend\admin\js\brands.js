// ===== BRANDS MANAGEMENT SCRIPT =====

// Global variables for brands
let brands = [
    { brand_id: 1, name: 'Apple', description: 'American technology company', logo_url: 'https://via.placeholder.com/40x40/007bff/ffffff?text=A', product_count: 5 },
    { brand_id: 2, name: 'Samsung', description: 'South Korean multinational conglomerate', logo_url: 'https://via.placeholder.com/40x40/28a745/ffffff?text=S', product_count: 6 },
    { brand_id: 3, name: 'Google', description: 'American technology company', logo_url: 'https://via.placeholder.com/40x40/ffc107/000000?text=G', product_count: 3 },
    { brand_id: 4, name: 'OnePlus', description: 'Chinese smartphone manufacturer', logo_url: 'https://via.placeholder.com/40x40/dc3545/ffffff?text=O', product_count: 2 },
    { brand_id: 5, name: '<PERSON><PERSON>', description: 'Chinese electronics company', logo_url: 'https://via.placeholder.com/40x40/6f42c1/ffffff?text=X', product_count: 4 }
];

// Make brands globally accessible
window.brands = brands;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Prevent multiple executions
    if (window.brandsLoaded) {
        return;
    }
    window.brandsLoaded = true;

    // Load brands immediately
    loadBrands(brands);
    initializeBrandsEventListeners();
});

// ===== EVENT LISTENERS =====
function initializeBrandsEventListeners() {
    // Add Brand button
    const addBrandBtn = document.getElementById('add-brand-btn');
    if (addBrandBtn && !addBrandBtn.hasAttribute('data-listener-added')) {
        addBrandBtn.addEventListener('click', function() {
            showBrandModal();
        });
        addBrandBtn.setAttribute('data-listener-added', 'true');
    }

    // Modal buttons
    const cancelBtn = document.getElementById('cancel-btn');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

    if (cancelBtn && !cancelBtn.hasAttribute('data-listener-added')) {
        cancelBtn.addEventListener('click', hideBrandModal);
        cancelBtn.setAttribute('data-listener-added', 'true');
    }

    if (cancelDeleteBtn && !cancelDeleteBtn.hasAttribute('data-listener-added')) {
        cancelDeleteBtn.addEventListener('click', hideDeleteModal);
        cancelDeleteBtn.setAttribute('data-listener-added', 'true');
    }

    if (confirmDeleteBtn && !confirmDeleteBtn.hasAttribute('data-listener-added')) {
        confirmDeleteBtn.addEventListener('click', function() {
            const brandId = confirmDeleteBtn.getAttribute('data-id');
            deleteBrand(brandId);
            hideDeleteModal();
            showNotification('Brand deleted successfully');
        });
        confirmDeleteBtn.setAttribute('data-listener-added', 'true');
    }

    // Close buttons
    const closeBtns = document.querySelectorAll('.close-btn');
    closeBtns.forEach(btn => {
        if (!btn.hasAttribute('data-listener-added')) {
            btn.addEventListener('click', function() {
                hideBrandModal();
                hideDeleteModal();
            });
            btn.setAttribute('data-listener-added', 'true');
        }
    });

    // Brand form submission
    const brandForm = document.getElementById('brand-form');
    if (brandForm && !brandForm.hasAttribute('data-listener-added')) {
        brandForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveBrand();
        });
        brandForm.setAttribute('data-listener-added', 'true');
    }

    }
// ===== LOAD BRANDS FUNCTION =====
function loadBrands(brands) {
    const tableBody = document.getElementById('brands-table-body');

    if (!tableBody) {
        console.error('❌ Brands table body not found');
        return;
    }

    // Clear existing content
    tableBody.innerHTML = '';

    if (!brands || brands.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="5" style="text-align: center; color: #666; padding: 40px;">No brands found</td>';
        tableBody.appendChild(row);
        return;
    }

    brands.forEach((brand, index) => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${brand.brand_id}</td>
            <td>
                <img src="${brand.logo_url}" alt="${brand.name}" class="brand-logo"
                     style="width: 40px; height: 40px; object-fit: contain; border-radius: 4px; border: 1px solid #ddd;"
                     onerror="this.src='https://via.placeholder.com/40x40/6c757d/ffffff?text=${brand.name.charAt(0)}';">
            </td>
            <td>
                <strong>${brand.name}</strong><br>
                <small style="color: #666;">${brand.description || ''}</small>
            </td>
            <td>
                <span class="badge" style="background-color: #007bff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">
                    ${brand.product_count}
                </span>
            </td>
            <td class="actions-cell">
                <button class="action-btn edit-btn" data-id="${brand.brand_id}" title="Edit brand"
                        style="background-color: #007bff; color: white; border: none; padding: 6px 10px; margin: 0 2px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" data-id="${brand.brand_id}" title="Delete brand"
                        style="background-color: #dc3545; color: white; border: none; padding: 6px 10px; margin: 0 2px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
        });

    // Add event listeners for edit and delete buttons
    addBrandActionListeners();
    }
// ===== ACTION LISTENERS =====
function addBrandActionListeners() {
    const editBtns = document.querySelectorAll('.edit-btn');
    const deleteBtns = document.querySelectorAll('.delete-btn');

    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const brandId = this.getAttribute('data-id');
            editBrand(brandId);
        });
    });

    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const brandId = this.getAttribute('data-id');
            showDeleteModal(brandId);
        });
    });
}

// ===== CRUD OPERATIONS =====
function deleteBrand(brandId) {
    const index = brands.findIndex(brand => brand.brand_id == brandId);
    if (index !== -1) {
        const brandName = brands[index].name;
        brands.splice(index, 1);
        window.brands = brands;
        loadBrands(brands);
        }
}
function editBrand(brandId) {
    const brand = brands.find(brand => brand.brand_id == brandId);
    if (!brand) {
        console.error('❌ Brand not found:', brandId);
        return;
    }

    // Populate form
    const brandIdField = document.getElementById('brand-id');
    const brandNameField = document.getElementById('brand-name');
    const brandDescField = document.getElementById('brand-description');
    const brandLogoField = document.getElementById('brand-logo');
    const modalTitle = document.getElementById('modal-title');

    if (brandIdField) brandIdField.value = brand.brand_id;
    if (brandNameField) brandNameField.value = brand.name;
    if (brandDescField) brandDescField.value = brand.description;
    if (brandLogoField) brandLogoField.value = brand.logo_url;
    if (modalTitle) modalTitle.textContent = 'Edit Brand';

    showBrandModal();
}

function saveBrand() {
    const brandId = document.getElementById('brand-id').value;
    const name = document.getElementById('brand-name').value;
    const description = document.getElementById('brand-description').value;
    const logoUrl = document.getElementById('brand-logo').value;

    if (!name.trim()) {
        alert('Brand name is required');
        return;
    }

    const newBrand = {
        brand_id: brandId || (brands.length + 1),
        name: name.trim(),
        description: description.trim(),
        logo_url: logoUrl.trim() || `https://via.placeholder.com/40x40/6c757d/ffffff?text=${name.charAt(0)}`,
        product_count: 0
    };

    if (brandId) {
        const index = brands.findIndex(brand => brand.brand_id == brandId);
        if (index !== -1) {
            brands[index] = newBrand;
            }
    } else {
        brands.push(newBrand);
        }

    window.brands = brands;
    loadBrands(brands);
    hideBrandModal();
    showNotification('Brand saved successfully');
}

// ===== MODAL FUNCTIONS =====
function showBrandModal() {
    const modal = document.getElementById('brand-modal');
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('active');
        }
}

function hideBrandModal() {
    const modal = document.getElementById('brand-modal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');

        const form = document.getElementById('brand-form');
        if (form) form.reset();

        const brandIdField = document.getElementById('brand-id');
        if (brandIdField) brandIdField.value = '';

        const modalTitle = document.getElementById('modal-title');
        if (modalTitle) modalTitle.textContent = 'Add New Brand';

        }
}

function showDeleteModal(brandId) {
    const modal = document.getElementById('delete-modal');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

    if (modal && confirmDeleteBtn) {
        confirmDeleteBtn.setAttribute('data-id', brandId);
        modal.style.display = 'flex';
        modal.classList.add('active');
        }
}

function hideDeleteModal() {
    const modal = document.getElementById('delete-modal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');
        }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification success';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #d4edda;
        color: #155724;
        padding: 15px 20px;
        border-radius: 4px;
        border-left: 4px solid #28a745;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);

    }

