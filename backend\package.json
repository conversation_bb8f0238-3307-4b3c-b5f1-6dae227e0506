{"name": "phonehub-ecommerce-backend", "version": "1.0.0", "description": "Backend for PhoneHub E-Commerce platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.0"}, "devDependencies": {"nodemon": "^2.0.22"}}