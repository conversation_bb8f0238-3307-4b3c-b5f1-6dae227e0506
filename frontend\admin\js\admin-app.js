// Admin App Common Functions and Variables
// API URL - same as main app but can be configured differently if needed
const API_URL = 'http://localhost:3000/api';
window.API_URL = API_URL;

// Utility function for making API requests with fallback data
window.apiRequest = async function(endpoint, options = {}) {
    try {
        const url = `${API_URL}${endpoint}`;
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error(`API request error for ${endpoint}:`, error);
        return null; // Return null to indicate the request failed
    }
};

// Format currency
window.formatCurrency = function(amount) {
    return `${parseFloat(amount).toLocaleString()} DZD`;
};

// Format date
window.formatDate = function(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
};

// Show notification
window.showNotification = function(message, type = 'success') {
    // Check if notification container exists, if not create it
    let notificationContainer = document.querySelector('.notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button class="close-notification"><i class="fas fa-times"></i></button>
    `;

    // Add to container
    notificationContainer.appendChild(notification);

    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Auto-hide after 5 seconds
    const hideTimeout = setTimeout(() => {
        hideNotification(notification);
    }, 5000);

    // Close button
    const closeBtn = notification.querySelector('.close-notification');
    closeBtn.addEventListener('click', () => {
        clearTimeout(hideTimeout);
        hideNotification(notification);
    });
};

// Hide notification helper
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Auth functionality
document.addEventListener('DOMContentLoaded', () => {
    // Logout button functionality
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '../login.html';
        });
    }

    // Check if user is logged in - only for non-login pages
    const isLoginPage = window.location.pathname.endsWith('login.html');
    if (!isLoginPage) {
        const adminToken = localStorage.getItem('admin_token');
        const adminUser = localStorage.getItem('admin_user');

        // If not logged in and not on login page, redirect to login
        if (!adminToken || !adminUser) {
            window.location.href = '../login.html';
        } else {
            }
    } else {
        }
});