# RaoufStore E-Commerce Platform

A modern, full-stack e-commerce platform built with Node.js, Express, PostgreSQL, and vanilla JavaScript.

## 🚀 Features

### 🛍️ Customer Features
- Product browsing and search
- Shopping cart functionality
- User authentication (login/register)
- Responsive mobile design
- Product filtering and sorting
- Secure checkout process

### 👨‍💼 Admin Features
- Dashboard with analytics
- Product management (CRUD)
- User management
- Order management
- Category and brand management
- Sales reporting

### 🔧 Technical Features
- JWT authentication
- PostgreSQL database
- RESTful API
- Responsive design
- Real-time updates
- Input validation and sanitization

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **PostgreSQL** - Database
- **JWT** - Authentication
- **bcrypt** - Password hashing
- **CORS** - Cross-origin resource sharing

### Frontend
- **HTML5** - Markup
- **CSS3** - Styling
- **JavaScript (ES6+)** - Client-side logic
- **Responsive Design** - Mobile-first approach

## 📁 Project Structure

```
raoufstore/
├── backend/                 # Backend API server
│   ├── config/             # Database configuration
│   ├── middleware/         # Express middleware
│   ├── routes/            # API routes
│   ├── .env               # Environment variables
│   ├── package.json       # Backend dependencies
│   └── server.js          # Main server file
├── frontend/              # Frontend web application
│   ├── admin/            # Admin panel
│   │   ├── css/          # Admin stylesheets
│   │   ├── js/           # Admin JavaScript
│   │   └── *.html        # Admin pages
│   ├── css/              # Main stylesheets
│   ├── js/               # Main JavaScript files
│   ├── images/           # Static images
│   └── *.html            # Customer pages
├── init-db/              # Database initialization
│   ├── 01-schema.sql     # Database schema
│   └── 02-seed-data.sql  # Sample data
├── frontend-server.js    # Frontend development server
└── README.md            # Project documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### 1. Database Setup
```sql
-- Create database
CREATE DATABASE ecommerce_phones;

-- Run initialization scripts
psql -U postgres -d ecommerce_phones -f init-db/01-schema.sql
psql -U postgres -d ecommerce_phones -f init-db/02-seed-data.sql
```

### 2. Backend Setup
```bash
# Install dependencies
cd backend
npm install

# Configure environment (update backend/.env with your settings)
# Start backend server
npm start
```

### 3. Frontend Setup

#### Option A: Using Live Server (Recommended for Development)
1. Install Live Server extension in VS Code
2. Right-click on `frontend/index.html`
3. Select "Open with Live Server"
4. Frontend will be available at: http://127.0.0.1:5500

#### Option B: Using Node.js Frontend Server
```bash
# Start frontend server (from project root)
node frontend-server.js
```

### 4. Access the Application
- **Customer Store**: http://127.0.0.1:5500 (Live Server) or http://localhost:8080 (Node server)
- **Admin Panel**: http://127.0.0.1:5500/admin (Live Server) or http://localhost:8080/admin (Node server)
- **API**: http://localhost:3000/api

## 🔐 Default Admin Credentials
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`

## 📡 API Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/health` | GET | Server health check | No |
| `/api/users/register` | POST | User registration | No |
| `/api/users/login` | POST | User login | No |
| `/api/products` | GET | Get all products | No |
| `/api/products/featured` | GET | Get featured products | No |
| `/api/carts` | GET | Get user cart | Yes |
| `/api/carts/items` | POST | Add item to cart | Yes |
| `/api/orders` | GET | Get user orders | Yes |

## 🔧 Development

### Environment Variables
Create `backend/.env` file:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_phones
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
PORT=3000
FRONTEND_URL=http://localhost:8080
```

### Development Mode
```bash
# Backend with auto-reload
cd backend
npm run dev

# Frontend server
node frontend-server.js
```

## 🧪 Testing
- Visit http://localhost:8080 to test the customer interface
- Visit http://localhost:8080/admin to test the admin panel
- Use browser developer tools to monitor API calls

## 📝 License
This project is licensed under the MIT License.

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support
For support, create an issue in the repository.