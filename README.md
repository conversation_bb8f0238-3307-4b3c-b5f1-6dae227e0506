# PhoneHub E-Commerce Platform

A complete e-commerce solution for selling mobile phones and accessories, built with Node.js/Express backend and HTML/CSS/JavaScript frontend.

## Project Architecture

```
ecommerce/
├── backend/                  # Node.js Express backend
│   ├── config/               # Configuration files
│   │   └── db.js             # Database connection
│   ├── middleware/           # Express middleware
│   │   └── auth.js           # Authentication middleware
│   ├── routes/               # API routes
│   │   ├── admin.js          # Admin dashboard routes
│   │   ├── carts.js          # Shopping cart routes
│   │   ├── orders.js         # Order management routes
│   │   ├── products.js       # Product routes
│   │   └── users.js          # User authentication routes
│   ├── Dockerfile            # Docker configuration for backend
│   └── server.js             # Express application entry point
│
├── frontend/                 # Static HTML/CSS/JS frontend
│   ├── admin/                # Admin dashboard HTML pages
│   │   ├── css/              # CSS files for admin
│   │   ├── js/               # JavaScript files for admin
│   │   └── index.html        # Admin dashboard home
│   ├── css/                  # CSS stylesheets
│   │   └── style.css         # Main stylesheet
│   ├── images/               # Image assets
│   │   ├── placeholder.svg   # Placeholder image
│   │   └── real-products/    # Real product images
│   ├── js/                   # JavaScript files
│   │   └── app.js            # Main JavaScript file
│   └── index.html            # Homepage
│
├── init-db/                  # Database initialization
│   ├── 01-schema.sql         # Database schema
│   ├── 02-seed-data.sql      # Sample data
│   └── migrate-real-images.sql # Script to update product images
│
├── download-product-images.sh # Bash script to download product images
├── download-product-images.ps1 # PowerShell script to download product images
├── cleanup.bat                # Batch file to remove redundant files (Windows)
├── delete_unnecessary_files.sh # Bash script to remove redundant files
├── delete_unnecessary_files.ps1 # PowerShell script to remove redundant files
├── docker-compose.yml        # Docker Compose configuration
├── nginx.conf                # Nginx server configuration
└── README.md                 # Project documentation
```

## Features

- **E-commerce Functionality**:
  - Product listings and search
  - Shopping cart and checkout
  - User accounts and order history
  - Responsive design for mobile and desktop

- **Admin Dashboard**:
  - Manage products, categories, and brands
  - Process and track orders
  - Manage user accounts
  - Set up promotions and discounts 

- **Technical Features**:
  - RESTful API with Node.js/Express
  - PostgreSQL database
  - Docker containerization
  - JWT authentication
  - Real product data and images

## Setup & Installation

### Prerequisites
- Docker and Docker Compose
- Node.js and npm (for development)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/phonehub-ecommerce.git
   cd phonehub-ecommerce
   ```

2. Clean up unnecessary files (optional):
   - For Unix/Linux/macOS:
     ```bash
     chmod +x delete_unnecessary_files.sh
     ./delete_unnecessary_files.sh
     ```
   - For Windows:
     ```
     cleanup.bat
     ```
     or if PowerShell is preferred:
     ```powershell
     powershell -ExecutionPolicy Bypass -File delete_unnecessary_files.ps1
     ```

3. Download real product images:
   - For Unix/Linux/macOS:
     ```bash
     chmod +x download-product-images.sh
     ./download-product-images.sh
     ```
   - For Windows:
     ```powershell
     powershell -ExecutionPolicy Bypass -File download-product-images.ps1
     ```

4. Start the application with Docker:
   ```bash
   docker-compose up -d
   ```

5. Initialize the database with real product images:
   ```bash
   docker exec -i ecommerce-db psql -U postgres -d ecommerce < init-db/migrate-real-images.sql
   ```

6. Access the application:
   - Frontend: http://localhost
   - Admin Dashboard: http://localhost/admin
   - API: http://localhost:3000/api

### Development Setup

1. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

2. Run the backend in development mode:
   ```bash
   npm run dev
   ```

## API Documentation

The backend exposes the following API endpoints:

- **Products**: 
  - `GET /api/products` - Get all products
  - `GET /api/products/featured` - Get featured products
  - `GET /api/products/:id` - Get product by ID
  - `POST /api/products` - Create new product (admin)
  - `PUT /api/products/:id` - Update product (admin)
  - `DELETE /api/products/:id` - Delete product (admin)

- **Users**:
  - `POST /api/users/register` - Register new user
  - `POST /api/users/login` - User login
  - `GET /api/users/profile` - Get user profile

- **Shopping Cart**:
  - `GET /api/carts` - Get current user's cart
  - `POST /api/carts/items` - Add item to cart
  - `PUT /api/carts/items/:id` - Update cart item
  - `DELETE /api/carts/items/:id` - Remove item from cart

- **Orders**:
  - `POST /api/orders` - Create a new order
  - `GET /api/orders` - Get user's orders
  - `GET /api/orders/:id` - Get order details

- **Admin**:
  - Various endpoints for dashboard functionality

## Database Schema

The application uses a PostgreSQL database with the following main tables:
- `users` - User accounts
- `products` - Product information
- `categories` - Product categories
- `brands` - Product brands
- `product_images` - Product images (with real image paths)
- `orders` - Customer orders
- `order_items` - Items in each order
- `shopping_carts` - Shopping carts
- `cart_items` - Items in shopping carts
- `promotions` - Discounts and promotions

## Recent Changes

1. **Admin Dashboard Relocation**: The admin dashboard has been moved from the backend to the frontend directory to maintain a cleaner project structure and to serve all static files from a single location.

2. **Real Product Images**: Added real product images (from Pexels) to replace the placeholder SVG images. These images are stored in the `frontend/images/real-products/` directory and referenced in the database.

3. **Database Updates**: Added a migration script (`init-db/migrate-real-images.sql`) to update the product images in the database to use the real image paths.

4. **Improved Frontend**: Updated the frontend JavaScript to use real product images from the database.

5. **Cleanup Scripts**: Added scripts to remove redundant files and directories from the project, including:
   - `cleanup.bat` - Windows batch file (simplest option for Windows users)
   - `delete_unnecessary_files.sh` - Bash script for Unix/Linux/macOS
   - `delete_unnecessary_files.ps1` - PowerShell script for Windows
   
   These scripts remove:
   - `backend/public` (moved to frontend/admin)
   - `backend/backend` (redundant directory)
   - Unused database files

## License

This project is licensed under the MIT License - see the LICENSE file for details. 