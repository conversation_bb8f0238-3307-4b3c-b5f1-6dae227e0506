document.addEventListener('DOMContentLoaded', function() {
    // State variables
    let categories = [];
    let currentPage = 1;
    let totalPages = 1;
    let categoriesPerPage = 10;
    let currentCategoryId = null;
    
    // DOM elements
    const categoriesTableBody = document.getElementById('categories-table-body');
    const addCategoryBtn = document.getElementById('add-category-btn');
    const categoryModal = document.getElementById('category-modal');
    const deleteModal = document.getElementById('delete-modal');
    const categoryForm = document.getElementById('category-form');
    const modalTitle = document.getElementById('modal-title');
    const searchInput = document.getElementById('category-search');
    const searchBtn = document.getElementById('search-btn');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageInfo = document.getElementById('page-info');
    const parentCategorySelect = document.getElementById('parent-category');
    
    // Close buttons for modals
    const closeButtons = document.querySelectorAll('.close-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    
    // Initialize the page
    init();
    
    function init() {
        fetchCategories();
        setupEventListeners();
    }
    
    function setupEventListeners() {
        // Add new category button
        addCategoryBtn.addEventListener('click', () => openAddModal());
        
        // Form submission
        categoryForm.addEventListener('submit', handleFormSubmit);
        
        // Close modal buttons
        closeButtons.forEach(button => {
            button.addEventListener('click', closeModals);
        });
        
        // Cancel buttons
        cancelBtn.addEventListener('click', closeModals);
        cancelDeleteBtn.addEventListener('click', closeModals);
        
        // Confirm delete
        confirmDeleteBtn.addEventListener('click', deleteCategory);
        
        // Search functionality
        searchInput.addEventListener('input', debounce(function() {
            console.log('Search input changed:', searchInput.value);
            currentPage = 1;
            fetchCategories();
        }, 300));

        // Search button click
        searchBtn.addEventListener('click', function() {
            console.log('Search button clicked');
            currentPage = 1;
            fetchCategories();
        });

        // Search on Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('Enter key pressed in search');
                e.preventDefault();
                currentPage = 1;
                fetchCategories();
            }
        });

        // Clear search button (if exists)
        const clearSearchBtn = document.getElementById('clear-search-btn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                currentPage = 1;
                fetchCategories();
            });
        }
        
        // Pagination
        prevPageBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                fetchCategories();
            }
        });
        
        nextPageBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                fetchCategories();
            }
        });
    }
    
    function fetchCategories() {
        const searchTerm = searchInput.value.trim().toLowerCase();

        // For demo purposes, use dummy data directly
        // In production, this would be an API call
        console.log('Loading categories with search term:', searchTerm);

        try {
            // Get all dummy categories
            let allCategories = getDummyCategories();

            // Filter categories based on search term
            if (searchTerm) {
                allCategories = allCategories.filter(category =>
                    category.name.toLowerCase().includes(searchTerm) ||
                    (category.description && category.description.toLowerCase().includes(searchTerm))
                );
            }

            // Calculate pagination
            const startIndex = (currentPage - 1) * categoriesPerPage;
            const endIndex = startIndex + categoriesPerPage;
            categories = allCategories.slice(startIndex, endIndex);
            totalPages = Math.ceil(allCategories.length / categoriesPerPage);

            // Update UI
            renderCategories();
            updatePagination();
            populateParentCategorySelect();

            console.log(`Loaded ${categories.length} categories (${allCategories.length} total)`);

        } catch (error) {
            console.error('Error loading categories:', error);
            showNotification('Failed to load categories', 'error');
            categories = [];
            renderCategories();
        }
    }
    
    function renderCategories() {
        categoriesTableBody.innerHTML = '';
        
        if (categories.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="empty-table">No categories found</td>';
            categoriesTableBody.appendChild(row);
            return;
        }
        
        categories.forEach(category => {
            const row = document.createElement('tr');
            
            const parentName = category.parent_category 
                ? category.parent_category.name 
                : '<span class="text-muted">None</span>';
            
            row.innerHTML = `
                <td>${category.category_id}</td>
                <td>${category.name}</td>
                <td>${parentName}</td>
                <td>${category.product_count || 0}</td>
                <td class="actions-cell">
                    <button class="action-btn edit-btn" data-id="${category.category_id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${category.category_id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            
            categoriesTableBody.appendChild(row);
        });
        
        // Add event listeners to edit and delete buttons
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', () => {
                const categoryId = button.getAttribute('data-id');
                openEditModal(categoryId);
            });
        });
        
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', () => {
                const categoryId = button.getAttribute('data-id');
                openDeleteModal(categoryId);
            });
        });
    }
    
    function populateParentCategorySelect() {
        // Clear existing options except the first one
        while (parentCategorySelect.options.length > 1) {
            parentCategorySelect.remove(1);
        }

        // Get all categories (not just current page) and filter parent categories
        const allCategories = getDummyCategories();
        const parentCategories = allCategories.filter(cat => !cat.parent_category_id);

        parentCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.category_id;
            option.textContent = category.name;
            parentCategorySelect.appendChild(option);
        });
    }
    
    function updatePagination() {
        pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
        
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
    }
    
    function openAddModal() {
        modalTitle.textContent = 'Add New Category';
        categoryForm.reset();
        document.getElementById('category-id').value = '';
        currentCategoryId = null;
        
        categoryModal.classList.add('active');
    }
    
    function openEditModal(categoryId) {
        const category = categories.find(cat => cat.category_id == categoryId);
        if (!category) return;
        
        modalTitle.textContent = 'Edit Category';
        document.getElementById('category-id').value = category.category_id;
        document.getElementById('category-name').value = category.name;
        document.getElementById('category-description').value = category.description || '';
        
        // Set parent category
        const parentSelect = document.getElementById('parent-category');
        if (category.parent_category_id) {
            parentSelect.value = category.parent_category_id;
        } else {
            parentSelect.value = '';
        }
        
        currentCategoryId = categoryId;
        categoryModal.classList.add('active');
    }
    
    function openDeleteModal(categoryId) {
        const category = categories.find(cat => cat.category_id == categoryId);
        if (!category) return;
        
        currentCategoryId = categoryId;
        
        // Check if category has subcategories
        const hasSubcategories = categories.some(cat => cat.parent_category_id == categoryId);
        
        const warningText = document.querySelector('#delete-modal .warning-text');
        if (hasSubcategories) {
            warningText.textContent = 'Warning: This category has subcategories that will also be affected.';
            warningText.style.display = 'block';
        } else {
            warningText.style.display = 'none';
        }
        
        deleteModal.classList.add('active');
    }
    
    function closeModals() {
        categoryModal.classList.remove('active');
        deleteModal.classList.remove('active');
    }
    
    function handleFormSubmit(e) {
        e.preventDefault();
        
        const categoryData = {
            name: document.getElementById('category-name').value,
            description: document.getElementById('category-description').value,
            parent_category_id: document.getElementById('parent-category').value || null
        };
        
        if (currentCategoryId) {
            // Update existing category
            updateCategory(currentCategoryId, categoryData);
        } else {
            // Create new category
            createCategory(categoryData);
        }
    }
    
    function createCategory(categoryData) {
        // API call to create category
        fetch('/api/admin/categories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify(categoryData)
        })
        .then(response => {
            if (!response.ok) throw new Error('Failed to create category');
            return response.json();
        })
        .then(data => {
            showNotification('Category created successfully', 'success');
            closeModals();
            fetchCategories();
        })
        .catch(error => {
            console.error('Error creating category:', error);
            showNotification('Failed to create category', 'error');
            
            // For demo purposes
            closeModals();
            mockCreateCategory(categoryData);
            fetchCategories();
        });
    }
    
    function updateCategory(categoryId, categoryData) {
        // API call to update category
        fetch(`/api/admin/categories/${categoryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify(categoryData)
        })
        .then(response => {
            if (!response.ok) throw new Error('Failed to update category');
            return response.json();
        })
        .then(data => {
            showNotification('Category updated successfully', 'success');
            closeModals();
            fetchCategories();
        })
        .catch(error => {
            console.error('Error updating category:', error);
            showNotification('Failed to update category', 'error');
            
            // For demo purposes
            closeModals();
            mockUpdateCategory(categoryId, categoryData);
            fetchCategories();
        });
    }
    
    function deleteCategory() {
        if (!currentCategoryId) return;
        
        // API call to delete category
        fetch(`/api/admin/categories/${currentCategoryId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`
            }
        })
        .then(response => {
            if (!response.ok) throw new Error('Failed to delete category');
            return response.json();
        })
        .then(data => {
            showNotification('Category deleted successfully', 'success');
            closeModals();
            fetchCategories();
        })
        .catch(error => {
            console.error('Error deleting category:', error);
            showNotification('Failed to delete category', 'error');
            
            // For demo purposes
            closeModals();
            mockDeleteCategory(currentCategoryId);
            fetchCategories();
        });
    }
    
    // Helper functions
    function getAuthToken() {
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    }
    
    function showNotification(message, type = 'info') {
        // Check if notification container exists
        let container = document.querySelector('.notification-container');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        container.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }
    
    // Mock functions for demo purposes
    function mockCreateCategory(categoryData) {
        const newCategory = {
            category_id: Math.floor(Math.random() * 1000) + 100,
            name: categoryData.name,
            description: categoryData.description,
            parent_category_id: categoryData.parent_category_id,
            product_count: 0
        };
        
        if (categoryData.parent_category_id) {
            const parentCategory = categories.find(cat => cat.category_id == categoryData.parent_category_id);
            if (parentCategory) {
                newCategory.parent_category = {
                    name: parentCategory.name
                };
            }
        }
        
        categories.unshift(newCategory);
        showNotification('Category created successfully', 'success');
    }
    
    function mockUpdateCategory(categoryId, categoryData) {
        const categoryIndex = categories.findIndex(cat => cat.category_id == categoryId);
        if (categoryIndex === -1) return;
        
        categories[categoryIndex] = {
            ...categories[categoryIndex],
            name: categoryData.name,
            description: categoryData.description,
            parent_category_id: categoryData.parent_category_id
        };
        
        if (categoryData.parent_category_id) {
            const parentCategory = categories.find(cat => cat.category_id == categoryData.parent_category_id);
            if (parentCategory) {
                categories[categoryIndex].parent_category = {
                    name: parentCategory.name
                };
            }
        } else {
            categories[categoryIndex].parent_category = null;
        }
        
        showNotification('Category updated successfully', 'success');
    }
    
    function mockDeleteCategory(categoryId) {
        const categoryIndex = categories.findIndex(cat => cat.category_id == categoryId);
        if (categoryIndex === -1) return;
        
        categories.splice(categoryIndex, 1);
        showNotification('Category deleted successfully', 'success');
    }
    
    function getDummyCategories() {
        return [
            {
                category_id: 1,
                name: 'Smartphones',
                description: 'All smartphone devices',
                parent_category_id: null,
                product_count: 6
            },
            {
                category_id: 2,
                name: 'Tablets',
                description: 'Tablet devices of all sizes',
                parent_category_id: null,
                product_count: 2
            },
            {
                category_id: 3,
                name: 'Accessories',
                description: 'Phone and tablet accessories',
                parent_category_id: null,
                product_count: 3
            },
            {
                category_id: 4,
                name: 'Android Phones',
                description: 'Smartphones running Android OS',
                parent_category_id: 1,
                parent_category: { name: 'Smartphones' },
                product_count: 4
            },
            {
                category_id: 5,
                name: 'iOS Phones',
                description: 'Apple iPhones running iOS',
                parent_category_id: 1,
                parent_category: { name: 'Smartphones' },
                product_count: 1
            },
            {
                category_id: 6,
                name: 'Windows Phones',
                description: 'Smartphones running Windows Mobile',
                parent_category_id: 1,
                parent_category: { name: 'Smartphones' },
                product_count: 0
            },
            {
                category_id: 7,
                name: 'Phone Cases',
                description: 'Protective cases for smartphones',
                parent_category_id: 3,
                parent_category: { name: 'Accessories' },
                product_count: 1
            },
            {
                category_id: 8,
                name: 'Chargers',
                description: 'Charging devices and cables',
                parent_category_id: 3,
                parent_category: { name: 'Accessories' },
                product_count: 1
            },
            {
                category_id: 9,
                name: 'Screen Protectors',
                description: 'Protective films and glass for screens',
                parent_category_id: 3,
                parent_category: { name: 'Accessories' },
                product_count: 1
            },
            {
                category_id: 10,
                name: 'Bluetooth Headphones',
                description: 'Wireless headphones using Bluetooth',
                parent_category_id: 3,
                parent_category: { name: 'Accessories' },
                product_count: 0
            }
        ];
    }
}); 